#!/bin/bash

# List of ports to be killed before starting the applications
PORTS=(4001 4002 4003 4004 4005 3000 8081)

# Check if lsof command is available
if ! command -v lsof &> /dev/null; then
    echo "lsof command not found. Please install lsof and try again."
    exit 1
fi

# Print all ports in use
echo "All ports currently in use:"
sudo lsof -i -P -n | grep LISTEN

# Function to kill the process using a specific port
kill_port() {
  PORT=$1
  echo "Checking port $PORT"
  PID=$(sudo lsof -t -i:$PORT)
  if [ -z "$PID" ]; then
    echo "No process found using port $PORT"
  else
    echo "Killing process $PID using port $PORT"
    sudo kill -9 $PID
    sleep 2 # Wait for a few seconds to ensure the port is freed
  fi
}

# Kill processes using the specified ports
for PORT in "${PORTS[@]}"; do
  kill_port $PORT
done

# Wait for a few seconds to ensure the ports are freed
sleep 2

# Navigate to Node.js API project and start it
cd soundscape-api/genres
npm install
npm run dev &
GENRES_API_PID=$!

# Navigate to Node.js Search API project and start it
cd ../../soundscape-api/search
npm install
npm run dev &
SEARCH_API_PID=$!

# Navigate to Node.js Player API project and start it
cd ../../soundscape-api/player
npm install
npm run dev &
PLAYER_API_PID=$!

# Navigate to Node.js Admin API project and start it
# cd ../../soundscape-api/admin
# npm install
# npm run dev &
# ADMIN_API_PID=$!

# Navigate to Node.js Streaming API project and start it
# cd ../../soundscape-api/streaming
# npm install
# npm run dev &
# STREAMING_API_PID=$!

# Navigate to Node.js Usage API project and start it
cd ../../soundscape-api/usage
npm install
npm run dev &
USAGE_API_PID=$!

# Navigate to React web app project and start it
# cd ../../soundscape-web
# npm install
# npm run start &
# WEB_PID=$!

# Navigate to React Native mobile app project and start it
# cd ../soundscape-mobile
# npm start &
# MOBILE_PID=$!

# Wait for all processes to complete
wait $GENRES_API_PID $SEARCH_API_PID $PLAYER_API_PID $STREAMING_API_PID $USAGE_API_PID $WEB_PID $ADMIN_API_PID