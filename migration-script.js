const { models, sequelize } = require('./soundscape-api/shared/config/database');
const { MongoClient, ObjectId } = require('mongodb');

async function migrateData() {
  // Connect to MongoDB
  const mongoClient = new MongoClient('mongodb://localhost:27017');
  await mongoClient.connect();
  const db = mongoClient.db('v5');

  // In-memory lookup tables
  const artistMap = new Map();
  const genreMap = new Map();
  const albumMap = new Map();
  const trackMap = new Map();

  try {
    // Step 1: Migrate artists
    console.log('Migrating artists...');
    const artists = await db.collection('massive_artist').find({}).toArray();

    // Process in batches of 100
    const BATCH_SIZE = 100;
    for (let i = 0; i < artists.length; i += BATCH_SIZE) {
      const batch = artists.slice(i, i + BATCH_SIZE);
      const artistsToCreate = batch.map(artist => ({
        name: artist.artistdisplay || artist.name || 'Unknown Artist',
        url: artist.url || null,
        image: artist.image || null,
        is_active: true,
        created_date: artist.created || new Date(),
        updated_date: new Date()
      }));

      // Use transaction and bulk create
      await sequelize.transaction(async (t) => {
        const createdArtists = await models.artist.bulkCreate(artistsToCreate, { transaction: t });

        // Update mapping
        batch.forEach((artist, index) => {
          artistMap.set(artist._id.toString(), createdArtists[index].id);
        });
      });

      console.log(`Migrated artists: ${Math.min((i + BATCH_SIZE), artists.length)}/${artists.length}`);
    }

    // Step 2: Migrate genres
    console.log('Migrating genres...');
    const genres = await db.collection('massive_genre').find({}).toArray();
    for (const genre of genres) {
      const newGenre = await models.genre.create({
        name: genre.name,
        url: genre.url || genre.name.toLowerCase().replace(/\s+/g, '-'),
        description: genre.description || null,
        image: genre.image || null,
        is_active: true,
        is_public: true,
        created_date: genre.created || new Date(),
        updated_date: new Date()
      });
      genreMap.set(genre._id.toString(), newGenre.id);
      console.log(`Migrated genre: ${genre.name} (${genreMap.size}/${genres.length})`);
    }

    // Step 3: Migrate albums with artist lookups
    console.log('Migrating albums...');
    const albums = await db.collection('massive_album').find({}).toArray();
    for (const album of albums) {
      const artistId = artistMap.get(album.artist.toString());
      if (artistId) {
        const newAlbum = await models.album.create({
          title: album.title,
          artist_id: artistId,
          image: album.image || null,
          release_date: album.release_date || null,
          is_active: true,
          created_date: album.created || new Date(),
          updated_date: new Date()
        });
        albumMap.set(album._id.toString(), newAlbum.id);

        // Create album_artist association
        await models.album_artist.create({
          album_id: newAlbum.id,
          artist_id: artistId
        });

        console.log(`Migrated album: ${album.title} (${albumMap.size}/${albums.length})`);
      }
    }

    // Step 4: Migrate tracks with lookups
    console.log('Migrating tracks...');
    const tracks = await db.collection('massive_track').find({}).toArray();
    for (const track of tracks) {
      const artistId = artistMap.get(track.artist.toString());
      const albumId = track.album ? albumMap.get(track.album.toString()) : null;

      if (artistId) {
        const newTrack = await models.track.create({
          title: track.title,
          artist_id: artistId,
          album_id: albumId,
          duration: track.duration || 0,
          file_path: track.file_path || track.url || null,
          is_active: true,
          created_date: track.created || new Date(),
          updated_date: new Date()
        });
        trackMap.set(track._id.toString(), newTrack.id);

        // Create track_artist association
        await models.track_artist.create({
          track_id: newTrack.id,
          artist_id: artistId
        });

        console.log(`Migrated track: ${track.title} (${trackMap.size}/${tracks.length})`);
      }
    }

    // Step 5: Migrate playlists/channels
    console.log('Migrating playlists/channels...');
    const channels = await db.collection('massive_channel').find({}).toArray();
    for (const channel of channels) {
      try {
        await sequelize.transaction(async (t) => {
          // Create a spot schedule for the channel
          const spotSchedule = await models.spot_schedule.create({
            name: `${channel.name} Schedule`,
            is_active: true,
            created_date: channel.created || new Date(),
            updated_date: new Date()
          }, { transaction: t });

          const newChannel = await models.channel.create({
            name: channel.name || 'Unnamed Channel',
            description: channel.description || null,
            url: channel.url || channel.name?.toLowerCase().replace(/\s+/g, '-') || `channel-${Date.now()}`,
            image: channel.image || null,
            spot_id: spotSchedule.id,
            is_active: true,
            is_public: true,
            created_date: channel.created || new Date(),
            updated_date: new Date() // Fixed typo: update_date -> updated_date
          }, { transaction: t });

          // Associate channel with genres if available
          if (channel.genres && Array.isArray(channel.genres)) {
            const genreChannels = [];
            for (const genreName of channel.genres) {
              const genre = await models.genre.findOne({
                where: { name: genreName },
                transaction: t
              });
              if (genre) {
                genreChannels.push({
                  genre_id: genre.id,
                  channel_id: newChannel.id,
                  is_active: true,
                  is_public: true,
                  created_date: new Date(),
                  updated_date: new Date()
                });
              }
            }
            if (genreChannels.length > 0) {
              await models.genre_channel.bulkCreate(genreChannels, { transaction: t });
            }
          }

          // Associate tracks with channel
          if (channel.tracks && Array.isArray(channel.tracks)) {
            const channelTracks = [];
            for (const trackItem of channel.tracks) {
              let trackId, weight;

              if (typeof trackItem === 'object') {
                trackId = trackMap.get(trackItem.track?.toString());
                weight = trackItem.weight || 1;
              } else {
                trackId = trackMap.get(trackItem?.toString());
                weight = 1;
              }

              if (trackId) {
                channelTracks.push({
                  channel_id: newChannel.id,
                  track_id: trackId,
                  weight: weight
                });
              }
            }

            if (channelTracks.length > 0) {
              await models.channel_track.bulkCreate(channelTracks, { transaction: t });
            }
          }
        });

        console.log(`Migrated channel: ${channel.name} (${channels.indexOf(channel) + 1}/${channels.length})`);
      } catch (error) {
        console.error(`Error migrating channel ${channel.name || 'unknown'}:`, error);
      }
    }

    console.log('Migration complete!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoClient.close();
  }
}

migrateData().catch(console.error);
