# Southern Soundscape API

## Setup Instructions

### Option 1: Local Development Setup

1. Install shared dependencies first (required for all services):
   ```bash
   cd shared
   npm install
   ```

2. Install service-specific dependencies for each API:
   ```bash
   cd ../admin
   npm install
   # Repeat for other services...
   ```

3. Environment Configuration
   Create a `.env` file in the root of `soundscape-api/` with the following values:

   ```bash
   # Database Configuration
   DB_USER=ss                  # Local PostgreSQL username
   DB_PASSWORD=password        # Local PostgreSQL password
   DB_NAME=soundscape         # Database name
   DB_HOST=localhost         # Database host
   DB_PORT=5432             # Default PostgreSQL port

   # API Configuration
   NODE_ENV=development    # Environment (development/production)
   PORT=3000              # API port number

   # Service-specific ports (to run multiple services simultaneously)
   GENRES_PORT=3001
   STREAMING_PORT=3002
   USAGE_PORT=3003
   ADMIN_PORT=3004
   SEARCH_PORT=3005
   ```

4. Database Setup Scripts
    node createDatabase.js
    node initializeDatabase.js   

### Option 2: Docker Compose Setup (Recommended)

1. Prerequisites:
   - Install [Docker](https://docs.docker.com/get-docker/)
   - Install [Docker Compose](https://docs.docker.com/compose/install/)

2. Environment Setup:
   - Copy `.env.example` to `.env` (uses same format as above)
   - For Docker, `DB_HOST` should be `postgres` instead of `localhost`

3. Building and Running:
   ```bash
   # Build all services
   docker-compose build

   # Start all services
   docker-compose up

   # Start in detached mode (background)
   docker-compose up -d

   # Start specific services
   docker-compose up postgres genres-api streaming-api

   # View logs
   docker-compose logs

   # View logs for specific service
   docker-compose logs genres-api

   # Follow logs
   docker-compose logs -f

   # Stop all services
   docker-compose down

   # Stop and remove volumes (will delete database data)
   docker-compose down -v
   ```

4. Service URLs:
   - Proxy API: http://localhost:3000
   - Genres API: http://localhost:3001
   - Streaming API: http://localhost:3002
   - Usage API: http://localhost:3003
   - Admin API: http://localhost:3004
   - Search API: http://localhost:3005

5. Database:
   - PostgreSQL is available on `localhost:5432`
   - Data persists between restarts in Docker volume
   - Connect using any PostgreSQL client with:
     ```
     Host: localhost
     Port: 5432
     User: ss
     Password: password
     Database: soundscape
     ```

6. Common Docker Commands:
   ```bash
   # Rebuild specific service after changes
   docker-compose build genres-api
   
   # Restart specific service
   docker-compose restart genres-api
   
   # View running containers
   docker-compose ps
   
   # View container resources
   docker stats
   
   # Execute command in container
   docker-compose exec genres-api sh
   
   # Clean up unused images and volumes
   docker system prune
   ```

7. Development with Docker:
   - Source code is mounted as volumes
   - Changes to shared code affect all services
   - Some services may require rebuild after changes
   - Use `docker-compose logs` to debug issues

## Project Structure

```
soundscape-api/
├── shared/           # Shared utilities and configurations
├── genres/           # Genres management service
├── streaming/        # Audio streaming service
├── usage/           # Usage tracking service
└── admin/           # Admin management service
```

## Available Services

- Genres API: Music genre management
- Streaming API: Audio streaming functionality
- Usage API: User activity tracking
- Admin API: Administrative functions
- Search API: Search functionality
- Proxy API: API Gateway/Proxy

## Troubleshooting

1. If services can't connect to database:
   - Ensure postgres container is running: `docker-compose ps`
   - Check postgres logs: `docker-compose logs postgres`
   - Verify DB_HOST is set to `postgres` in `.env`

2. If changes aren't reflecting:
   - Rebuild the service: `docker-compose build <service-name>`
   - Restart the service: `docker-compose restart <service-name>`

3. If ports are already in use:
   - Check for running containers: `docker ps`
   - Stop conflicting services: `docker-compose down`
   - Modify port mappings in `docker-compose.yml`

## Development

For local development without Docker, each service can be run independently:

```bash
# Run genres service
cd genres
npm start

# Run streaming service
cd streaming
npm start
```

For more details about each service, refer to their individual README files.
