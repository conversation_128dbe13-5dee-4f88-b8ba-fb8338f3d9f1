# Admin API

The Admin API provides administrative capabilities for managing the Southern Soundscape platform.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the service:
```bash
npm start
```

## Development

Run in development mode:
```bash
npm run dev
```

## Testing

Run the test suite:
```bash
npm test                # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Generate coverage report
```

## Authentication

All endpoints require admin authentication. Include the Admin token in the Authorization header:
```
Authorization: Bearer <admin_token>
```

## API Endpoints

### User Management

#### GET /api/admin/users
List all users with pagination.

Query parameters:
- `limit`: Results per page (default: 50)
- `offset`: Pagination offset
- `status`: Filter by status (active|suspended|pending)

Response:
```json
{
  "total": "integer",
  "users": [
    {
      "id": "string",
      "email": "string",
      "status": "string",
      "created_at": "string",
      "last_login": "string"
    }
  ]
}
```

#### PUT /api/admin/users/:userId/status
Update user status.

Request body:
```json
{
  "status": "active|suspended",
  "reason": "string"
}
```

### Content Management

#### GET /api/admin/content/pending
List pending content for moderation.

#### PUT /api/admin/content/:contentId/approve
Approve content.

#### PUT /api/admin/content/:contentId/reject
Reject content with reason.

### System Management

#### GET /api/admin/system/health
Get system health status.

#### GET /api/admin/system/metrics
Get system metrics and statistics.

### Error Responses

All endpoints return standard error responses:

```json
{
  "error_code": "string",
  "error_description": "string"
}
```

Common error codes:
- `unauthorized`: Invalid or missing admin token
- `forbidden`: Insufficient privileges
- `not_found`: Resource not found
- `validation_error`: Invalid request parameters
- `server_error`: Internal server error

## Docker Support

Build the container:
```bash
./build.sh
```

Run the container:
```bash
./run.sh
```

## Contributing

1. Create a feature branch
2. Make changes and add tests
3. Run tests and ensure they pass
4. Submit pull request

## License

ISC
