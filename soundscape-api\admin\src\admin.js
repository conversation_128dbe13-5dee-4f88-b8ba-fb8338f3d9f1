const AdminService = require('./services/adminService');

module.exports = {
    // User Management
    createUser: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.createUser(req.body);
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },

    deleteUser: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            await adminService.deleteUser(req.params.userId);
            res.status(204).send();
        } catch (e) {
            next(e);
        }
    },

    // Album Management
    createAlbum: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.createAlbum(req.body);
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },

    deleteAlbum: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            await adminService.deleteAlbum(req.params.albumId);
            res.status(204).send();
        } catch (e) {
            next(e);
        }
    },

    // Channel Management
    createChannel: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.createChannel(req.body);
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },

    addTrackToChannel: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.addTrackToChannel(
                req.params.channelId,
                req.body
            );
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },

    // Batch Artist Operations
    createArtistsBatch: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.createArtistsBatch(req.body);
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },

    // Batch Album Operations
    createAlbumsBatch: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.createAlbumsBatch(req.body);
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },

    // Batch Track Operations
    addTracksToAlbum: async (req, res, next) => {
        try {
            const adminService = new AdminService();
            const result = await adminService.addTracksToAlbum(
                req.params.albumId,
                req.body
            );
            res.status(201).json(result);
        } catch (e) {
            next(e);
        }
    },
};
