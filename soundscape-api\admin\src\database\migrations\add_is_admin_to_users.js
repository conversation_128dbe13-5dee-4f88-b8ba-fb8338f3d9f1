'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.addColumn('users', 'is_admin', {
            type: Sequelize.BOOLEAN,
            defaultValue: false,
            allowNull: false
        });

        // Optionally set some existing users as admins
        // await queryInterface.sequelize.query(
        //     `UPDATE users SET is_admin = true WHERE email IN ('<EMAIL>')`
        // );
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.removeColumn('users', 'is_admin');
    }
};