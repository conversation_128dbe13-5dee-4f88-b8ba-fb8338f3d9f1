const AuthMiddleware = require('../../../shared/middleware/authMiddleware');
const db = require('../database');
const createError = require('http-errors');

class AdminAuthMiddleware {
    static async adminAuthMiddleware(req, res, next) {
        try {
            // First, apply the standard authentication
            await AuthMiddleware.authenticate(req, res, async () => {
                // After standard auth, check if user is admin
                const user = await db.models.user.findOne({
                    where: {
                        email: req.user.email,
                        is_active: true
                    }
                });

                if (!user || !user.is_admin) {
                    return res.status(403).json({
                        error_code: 'forbidden',
                        error_description: 'Admin access required'
                    });
                }

                // Add admin user info to request
                req.adminUser = user;
                next();
            });
        } catch (error) {
            console.error('Admin authentication error:', error);
            
            if (error.response && error.response.status === 401) {
                return res.status(401).json({
                    error_code: 'unauthorized',
                    error_description: 'Invalid or expired token'
                });
            }

            return res.status(500).json({
                error_code: 'server_error',
                error_description: 'Authentication service error'
            });
        }
    }
}

module.exports = AdminAuthMiddleware;