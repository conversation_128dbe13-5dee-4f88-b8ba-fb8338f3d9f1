
const admin = require('./admin');
const { AdminAuthMiddleware } = require('../../shared/middleware/authMiddleware');
const errorHandler = require('../../shared/middleware/errorHandler');

module.exports = function (app) {
    // Health check
    app.route('/health')
        .get((req, res) => res.sendStatus(200));

    // Apply admin authentication middleware to all admin routes
    app.use('/api/admin', AdminAuthMiddleware.adminAuthMiddleware);

    // User Management
    app.route('/api/admin/users')
        .get(admin.getUsers)
        .post(admin.createUser);

    app.route('/api/admin/users/:userId')
        .get(admin.getUser)
        .put(admin.updateUser)
        .delete(admin.deleteUser);

    // Album Management
    app.route('/api/admin/albums')
        .get(admin.getAlbums)
        .post(admin.createAlbum);

    app.route('/api/admin/albums/:albumId')
        .get(admin.getAlbum)
        .put(admin.updateAlbum)
        .delete(admin.deleteAlbum);

    // Track Management
    app.route('/api/admin/tracks')
        .get(admin.getTracks)
        .post(admin.createTrack);

    app.route('/api/admin/tracks/:trackId')
        .get(admin.getTrack)
        .put(admin.updateTrack)
        .delete(admin.deleteTrack);

    // Channel Management
    app.route('/api/admin/channels')
        .get(admin.getChannels)
        .post(admin.createChannel);

    app.route('/api/admin/channels/:channelId')
        .get(admin.getChannel)
        .put(admin.updateChannel)
        .delete(admin.deleteChannel);

    // Channel Tracks Management
    app.route('/api/admin/channels/:channelId/tracks')
        .get(admin.getChannelTracks)
        .post(admin.addTrackToChannel);

    app.route('/api/admin/channels/:channelId/tracks/:trackId')
        .delete(admin.removeTrackFromChannel)
        .put(admin.updateChannelTrackOrder);

    // Genre Management
    app.route('/api/admin/genres')
        .get(admin.getGenres)
        .post(admin.createGenre);

    app.route('/api/admin/genres/:genreId')
        .get(admin.getGenre)
        .put(admin.updateGenre)
        .delete(admin.deleteGenre);

    // Genre Channels Management
    app.route('/api/admin/genres/:genreId/channels')
        .get(admin.getGenreChannels)
        .post(admin.addChannelToGenre);

    app.route('/api/admin/genres/:genreId/channels/:channelId')
        .delete(admin.removeChannelFromGenre)
        .put(admin.updateGenreChannelOrder);

    // Artist Management
    app.route('/api/admin/artists')
        .get(admin.getArtists)
        .post(admin.createArtist);

    app.route('/api/admin/artists/:artistId')
        .get(admin.getArtist)
        .put(admin.updateArtist)
        .delete(admin.deleteArtist);

    // Batch Operations
    app.route('/api/admin/artists/batch')
        .post(admin.createArtistsBatch);

    app.route('/api/admin/albums/batch')
        .post(admin.createAlbumsBatch);

    app.route('/api/admin/albums/:albumId/tracks/batch')
        .post(admin.addTracksToAlbum);

    // Subscription Management
    app.route('/api/admin/subscriptions')
        .get(admin.getAllSubscriptions);

    app.route('/api/admin/users/:userId/subscription')
        .get(admin.getSubscription)
        .put(admin.updateSubscription);

    // Use shared error handler
    app.use(errorHandler);
};
