const db = require('../database');
const { Op } = require('sequelize');
const createError = require('http-errors');

class AdminService {
    // User Management
    async createUser(userData) {
        const { username, password, name, ...otherData } = userData;

        if (!username || !password || !name) {
            throw createError(400, 'Missing required fields');
        }

        return await db.models.user.create({
            username,
            password, // Note: Should be hashed before saving
            name,
            is_active: true,
            created_date: new Date(),
            ...otherData
        });
    }

    async deleteUser(userId) {
        const user = await db.models.user.findByPk(userId);
        if (!user) {
            throw createError(404, 'User not found');
        }
        await user.destroy();
        return { success: true };
    }

    // Album Management
    async createAlbum(albumData) {
        const { name, artist_id, release_date, ...otherData } = albumData;

        if (!name || !artist_id) {
            throw createError(400, 'Missing required fields');
        }

        return await db.models.album.create({
            name,
            artist_id,
            release_date,
            created_date: new Date(),
            ...otherData
        });
    }

    async deleteAlbum(albumId) {
        const album = await db.models.album.findByPk(albumId);
        if (!album) {
            throw createError(404, 'Album not found');
        }
        await album.destroy();
        return { success: true };
    }

    // Channel Management
    async createChannel(channelData) {
        const { name, description, ...otherData } = channelData;

        if (!name) {
            throw createError(400, 'Missing required fields');
        }

        return await db.models.channel.create({
            name,
            description,
            is_active: true,
            created_date: new Date(),
            ...otherData
        });
    }

    async addTrackToChannel(channelId, trackData) {
        const { track_id, order } = trackData;

        if (!track_id) {
            throw createError(400, 'Missing required fields');
        }

        // Verify channel and track exist
        const [channel, track] = await Promise.all([
            db.models.channel.findByPk(channelId),
            db.models.track.findByPk(track_id)
        ]);

        if (!channel || !track) {
            throw createError(404, 'Channel or track not found');
        }

        return await db.models.channel_track.create({
            channel_id: channelId,
            track_id,
            order: order || 0,
            created_date: new Date()
        });
    }

    // Track Management
    async createTrack(trackData) {
        const { name, album_id, duration, ...otherData } = trackData;

        if (!name || !album_id) {
            throw createError(400, 'Missing required fields');
        }

        return await db.models.track.create({
            name,
            album_id,
            duration,
            is_active: true,
            created_date: new Date(),
            ...otherData
        });
    }

    async updateTrackOrder(channelId, trackId, newOrder) {
        const channelTrack = await db.models.channel_track.findOne({
            where: {
                channel_id: channelId,
                track_id: trackId
            }
        });

        if (!channelTrack) {
            throw createError(404, 'Track not found in channel');
        }

        return await channelTrack.update({ order: newOrder });
    }

    // Genre Management
    async createGenre(genreData) {
        const { name, description, ...otherData } = genreData;

        if (!name) {
            throw createError(400, 'Missing required fields');
        }

        return await db.models.genre.create({
            name,
            description,
            is_active: true,
            is_public: true,
            created_date: new Date(),
            ...otherData
        });
    }

    // Artist Management
    async createArtist(artistData) {
        const { name, ...otherData } = artistData;

        if (!name) {
            throw createError(400, 'Missing required fields');
        }

        return await db.models.artist.create({
            name,
            is_active: true,
            created_date: new Date(),
            ...otherData
        });
    }

    // Batch Artist Operations
    async createArtistsBatch(artistsData) {
        if (!Array.isArray(artistsData) || artistsData.length === 0) {
            throw createError(400, 'Invalid artists data format');
        }

        const transaction = await db.connection.transaction();

        try {
            const artists = artistsData.map(artist => ({
                name: artist.name,
                description: artist.description,
                image: artist.image,
                url: artist.url,
                is_active: true,
                is_public: true,
                created_date: new Date(),
                updated_date: new Date()
            }));

            // Validate all artists have required fields
            if (artists.some(artist => !artist.name)) {
                throw createError(400, 'All artists must have a name');
            }

            const createdArtists = await db.models.artist.bulkCreate(artists, {
                transaction,
                validate: true
            });

            await transaction.commit();
            return createdArtists;
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    // Batch Album Operations
    async createAlbumsBatch(albumsData) {
        if (!Array.isArray(albumsData) || albumsData.length === 0) {
            throw createError(400, 'Invalid albums data format');
        }

        const transaction = await db.connection.transaction();

        try {
            // First, verify all referenced artists exist
            const artistIds = [...new Set(albumsData.map(album => album.artist_id))];
            const existingArtists = await db.models.artist.findAll({
                where: { id: artistIds },
                transaction
            });

            if (existingArtists.length !== artistIds.length) {
                throw createError(400, 'One or more artist IDs are invalid');
            }

            // Verify all referenced labels exist
            const labelIds = [...new Set(albumsData.map(album => album.label_id))];
            const existingLabels = await db.models.label.findAll({
                where: { id: labelIds },
                transaction
            });

            if (existingLabels.length !== labelIds.length) {
                throw createError(400, 'One or more label IDs are invalid');
            }

            const albums = albumsData.map(album => ({
                title: album.title,
                description: album.description,
                url: album.url,
                image: album.image,
                year: album.year,
                song_total: album.song_total,
                hover_text: album.hover_text,
                artist_id: album.artist_id,
                label_id: album.label_id,
                is_active: true,
                is_public: true,
                created_date: new Date(),
                updated_date: new Date()
            }));

            // Validate all albums have required fields
            if (albums.some(album => !album.title || !album.artist_id || !album.label_id)) {
                throw createError(400, 'All albums must have a title, artist_id, and label_id');
            }

            const createdAlbums = await db.models.album.bulkCreate(albums, {
                transaction,
                validate: true
            });

            // Create album_artists associations
            const albumArtistAssociations = createdAlbums.map(album => ({
                album_id: album.id,
                artist_id: album.artist_id,
                role: 'primary',
                created_date: new Date(),
                updated_date: new Date()
            }));

            await db.models.album_artist.bulkCreate(albumArtistAssociations, {
                transaction,
                validate: true
            });

            await transaction.commit();
            return createdAlbums;
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    // Batch Track Operations for Albums
    async addTracksToAlbum(albumId, tracksData) {
        if (!Array.isArray(tracksData) || tracksData.length === 0) {
            throw createError(400, 'Invalid tracks data format');
        }

        const transaction = await db.connection.transaction();

        try {
            // Verify album exists
            const album = await db.models.album.findByPk(albumId, { transaction });
            if (!album) {
                throw createError(404, 'Album not found');
            }

            const tracks = tracksData.map(track => ({
                title: track.title,
                description: track.description,
                url: track.url,
                duration_seconds: track.duration_seconds,
                album_id: albumId,
                artist_id: track.artist_id || album.artist_id, // Use album's artist if not specified
                is_active: true,
                is_public: true,
                created_date: new Date(),
                updated_date: new Date()
            }));

            // Validate all tracks have required fields
            if (tracks.some(track => !track.title || !track.duration_seconds)) {
                throw createError(400, 'All tracks must have a title and duration');
            }

            const createdTracks = await db.models.track.bulkCreate(tracks, {
                transaction,
                validate: true
            });

            // Create track_artist associations
            const trackArtistAssociations = createdTracks.map(track => ({
                track_id: track.id,
                artist_id: track.artist_id,
                role: 'primary',
                created_date: new Date(),
                updated_date: new Date()
            }));

            await db.models.track_artist.bulkCreate(trackArtistAssociations, {
                transaction,
                validate: true
            });

            await transaction.commit();
            return createdTracks;
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }

    // Utility methods
    async _checkExists(model, id) {
        const instance = await db.models[model].findByPk(id);
        if (!instance) {
            throw createError(404, `${model} not found`);
        }
        return instance;
    }

    // Subscription Management
    async getSubscription(userId) {
        const subscription = await db.models.subscription.findOne({
            where: { user_id: userId },
            order: [['created_date', 'DESC']]
        });

        if (!subscription) {
            throw createError(404, 'Subscription not found');
        }

        return subscription;
    }

    async updateSubscription(userId, subscriptionData) {
        const subscription = await db.models.subscription.findOne({
            where: { user_id: userId },
            order: [['created_date', 'DESC']]
        });

        if (!subscription) {
            throw createError(404, 'Subscription not found');
        }

        // Admin can override end_date and status
        const updates = {
            end_date: subscriptionData.end_date,
            status: subscriptionData.status,
            updated_date: new Date()
        };

        if (subscriptionData.status === 'cancelled' && !subscription.cancelled_date) {
            updates.cancelled_date = new Date();
        }

        await subscription.update(updates);
        return subscription;
    }

    async getAllSubscriptions(query = {}) {
        const where = {};

        if (query.status) {
            where.status = query.status;
        }

        return await db.models.subscription.findAll({
            where,
            include: [{
                model: db.models.user,
                attributes: ['username', 'name', 'email']
            }],
            order: [['created_date', 'DESC']]
        });
    }
}

module.exports = AdminService;
