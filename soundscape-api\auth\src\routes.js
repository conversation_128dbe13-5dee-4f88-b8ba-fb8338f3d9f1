const express = require('express');
const authService = require('./services/authService');
const { authenticate } = require('../../shared/middleware/authMiddleware');
const errorHandler = require('../../shared/middleware/errorHandler');

module.exports = function (app) {
    // Health check
    app.route('/health')
        .get((req, res) => res.sendStatus(200));

    // OAuth routes
    app.route('/api/auth/login')
        .get((req, res) => {
            const state = Math.random().toString(36).substring(7);
            // Store state in session or cache for validation
            req.session.oauth_state = state;

            const authUrl = authService.getAuthorizationUrl(state);
            res.redirect(authUrl);
        });

    // Use shared error handler
    app.use(errorHandler);
};
