const express = require('express');
const session = require('express-session');
const cors = require('cors');
const config = require('./config');

const app = express();

// Middleware
app.use(cors(config.cors));
app.use(express.json());
app.use(session(config.session));

// Routes
require('./routes')(app);

// Error handling
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(err.status || 500).json({
        error: config.env === 'development' ? err.message : 'Internal server error'
    });
});

app.listen(config.port, () => {
    console.log(`Auth service listening on port ${config.port}`);
});