const axios = require('axios');
const createError = require('http-errors');
const db = require('../database');

class AuthService {
    constructor() {
        this.openPassConfig = {
            authorizationURL: process.env.OPENPASS_AUTH_URL,
            tokenURL: process.env.OPENPASS_TOKEN_URL,
            clientID: process.env.OPENPASS_CLIENT_ID,
            clientSecret: process.env.OPENPASS_CLIENT_SECRET,
            callbackURL: process.env.OPENPASS_CALLBACK_URL,
            userInfoURL: 'https://auth.myopenpass.com/v1/api/userinfo'
        };
    }

    async validateToken(token) {
        try {
            const userInfo = await this.getUserInfo(token);
            return {
                isValid: true,
                user: userInfo
            };
        } catch (error) {
            return {
                isValid: false,
                error: 'Invalid token'
            };
        }
    }

    async getUserInfo(token) {
        try {
            const response = await axios.get(this.openPassConfig.userInfoURL, {
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            });
            return response.data;
        } catch (error) {
            throw createError(401, 'Failed to fetch user info');
        }
    }

    async handleCallback(code) {
        try {
            const tokenResponse = await this.exchangeCodeForToken(code);
            const userInfo = await this.getUserInfo(tokenResponse.access_token);
            
            // Create or update user in our database
            const user = await this.upsertUser(userInfo);

            return {
                user,
                token: tokenResponse.access_token
            };
        } catch (error) {
            throw createError(401, 'Authentication failed');
        }
    }

    async exchangeCodeForToken(code) {
        try {
            const response = await axios.post(this.openPassConfig.tokenURL, {
                grant_type: 'authorization_code',
                client_id: this.openPassConfig.clientID,
                client_secret: this.openPassConfig.clientSecret,
                code,
                redirect_uri: this.openPassConfig.callbackURL
            });
            return response.data;
        } catch (error) {
            throw createError(401, 'Token exchange failed');
        }
    }

    async upsertUser(userInfo) {
        const [user] = await db.models.user.upsert({
            email: userInfo.email,
            name: userInfo.name,
            openpass_id: userInfo.sub,
            last_login: new Date(),
            is_active: true
        }, {
            returning: true,
            where: { email: userInfo.email }
        });

        return user;
    }

    getAuthorizationUrl(state) {
        const params = new URLSearchParams({
            response_type: 'code',
            client_id: this.openPassConfig.clientID,
            redirect_uri: this.openPassConfig.callbackURL,
            scope: 'openid profile email',
            state
        });

        return `${this.openPassConfig.authorizationURL}?${params.toString()}`;
    }
}

module.exports = new AuthService();