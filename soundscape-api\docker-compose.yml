version: '3'
services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: ss
      POSTGRES_PASSWORD: password
      POSTGRES_DB: soundscape
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  usage-api:
    build: ./usage
    ports:
      - "3003:80"
    volumes:
      - ./shared:/usr/src/app/shared
      - ./.env:/usr/src/app/.env
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

  streaming-api:
    build: ./streaming
    ports:
      - "3002:80"
    volumes:
      - ./shared:/usr/src/app/shared
      - ./.env:/usr/src/app/.env
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

  genres-api:
    build: ./genres
    ports:
      - "3001:80"
    volumes:
      - ./shared:/usr/src/app/shared
      - ./.env:/usr/src/app/.env
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

  admin-api:
    build: ./admin
    ports:
      - "3004:80"
    volumes:
      - ./shared:/usr/src/app/shared
      - ./.env:/usr/src/app/.env
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

  search-api:
    build: ./search
    ports:
      - "3005:80"
    volumes:
      - ./shared:/usr/src/app/shared
      - ./.env:/usr/src/app/.env
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

  proxy-api:
    build: ./proxy
    ports:
      - "3000:80"
    volumes:
      - ./shared:/usr/src/app/shared
      - ./.env:/usr/src/app/.env
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres
    depends_on:
      - postgres

volumes:
  postgres_data:
