# Genres API

The Genres API manages music genres and their relationships with channels in the Southern Soundscape platform.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the service:
```bash
npm start
```

## Development

Run in development mode:
```bash
npm run dev
```

## Testing

Run tests:
```bash
npm test                # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Generate coverage report
```

## API Documentation

### Health Check

#### GET /health
Returns 200 OK if service is healthy.

### Genre Management

#### GET /api/genres
Get list of all active genres.

Response:
```json
[
  {
    "id": "integer",
    "name": "string",
    "url": "string",
    "image": "string",
    "order": "integer"
  }
]
```

#### GET /api/genrechannels
Get channels for a specific genre.

Query Parameters:
- `genreId`: ID of the genre (required)

Response:
```json
{
  "genre": {
    "id": "integer",
    "name": "string",
    "description": "string",
    "image": "string",
    "path": "string"
  },
  "channels": [
    {
      "id": "integer",
      "name": "string",
      "description": "string",
      "image": "string",
      "order": "integer"
    }
  ]
}
```

### Error Responses

All endpoints return standard error responses:

```json
{
  "error_code": "string",
  "error_description": "string"
}
```

Common Error Codes:
- `invalid_request`: Missing or invalid parameters
- `not_found`: Resource not found
- `server_error`: Internal server error

## Database Schema

### Genre Table
```sql
CREATE TABLE genres (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    url VARCHAR(255),
    image VARCHAR(255),
    description TEXT,
    order INTEGER,
    is_active BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT true,
    created_date TIMESTAMP,
    updated_date TIMESTAMP
);
```

### Genre Channel Table
```sql
CREATE TABLE genre_channels (
    id SERIAL PRIMARY KEY,
    genre_id INTEGER REFERENCES genres(id),
    channel_id INTEGER REFERENCES channels(id),
    order INTEGER,
    created_date TIMESTAMP,
    updated_date TIMESTAMP
);
```

## Architecture

The service follows a layered architecture:
- Routes (`/src/routes.js`): API endpoint definitions
- Controllers (`/src/genres.js`): Request handling and response formatting
- Services (`/src/services/`): Business logic and data processing
- Database (`/src/database/`): Data access and models

## Contributing

1. Create a feature branch
2. Make changes and add tests
3. Run tests and ensure they pass
4. Submit pull request

## License

ISC
