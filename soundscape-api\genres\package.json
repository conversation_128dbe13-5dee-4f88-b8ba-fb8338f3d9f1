{"name": "ss-genres-api", "version": "1.0.0", "description": "Southern Soundscape Genres API", "main": "app.js", "scripts": {"start": "node app.js", "dev": "node app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "", "license": "ISC", "dependencies": {"cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.17.1", "fs": "^0.0.1-security", "http-errors": "^2.0.0", "node-cache": "^5.1.2", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.4"}}