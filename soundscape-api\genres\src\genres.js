const GenreService = require('./services/genreService');

module.exports = {

    getGenreChannels: async function (req, res, next) {
        const genreId = req.params.genreId || req.query.genreId;

        if (!genreId) {
            return next({
                status: 400,
                error_code: 'invalid_request',
                error_description: 'Genre ID is required'
            });
        }

        try {
            const genreService = new GenreService();
            const result = await genreService.getGenreChannelsByGenreId(genreId);
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    getGenres: async function (req, res, next) {
        try {
            console.log("call get genres.");
            const genreService = new GenreService();
            const result = await genreService.getGenres();
            res.json(result);
        } catch (e) {
            console.log(next(e));
            return next(e);
        }
    },
};
