
const express = require('express');
const genres = require('./genres');
const errorHandler = require('../../shared/middleware/errorHandler');

module.exports = function (app) {
    // Health check
    app.route('/health')
        .get((req, res) => res.sendStatus(200));

    app.route('/api/genrechannels')
        .get(genres.getGenreChannels);

    app.route('/api/genres')
        .get(genres.getGenres);

    // Use shared error handler
    app.use(errorHandler);
}
