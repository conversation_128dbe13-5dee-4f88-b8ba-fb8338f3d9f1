const request = require('supertest');
const express = require('express');
const app = express();
const GenreService = require('../src/services/genreService');
const db = require('../src/database');

// Mock database responses
jest.mock('../src/database', () => ({
    models: {
        genre: {
            findAll: jest.fn(),
            findOne: jest.fn(),
        },
        genre_channel: {  // Updated model name
            findAll: jest.fn(),
        },
        channel: {}
    },
}));

// Mock error handler
jest.mock('../../shared/middleware/errorHandler', () => {
    return jest.fn((err, req, res, next) => {
        res.status(err.status || 500).json({
            error_code: err.error_code || 'server_error',
            error_description: err.error_description || 'A server error occurred'
        });
    });
});

// Setup routes
require('../src/routes')(app);

describe('Genres API', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('GET /api/genres', () => {
        it('should return all active genres', async () => {
            const mockGenres = [
                {
                    id: 1,
                    name: 'Rock',
                    url: 'rock',
                    image: 'rock.jpg',
                    order: 1
                },
                {
                    id: 2,
                    name: 'Jazz',
                    url: 'jazz',
                    image: 'jazz.jpg',
                    order: 2
                }
            ];

            db.models.genre.findAll.mockResolvedValue(mockGenres);

            const response = await request(app)
                .get('/api/genres')
                .expect(200);

            expect(response.body).toEqual(mockGenres);
            expect(db.models.genre.findAll).toHaveBeenCalledWith({
                where: {
                    is_active: true,
                    is_public: true
                },
                attributes: ['id', 'url', 'name', 'image', 'order'],
                order: [['order', 'ASC']]
            });
        });

        it('should handle errors when fetching genres', async () => {
            db.models.genre.findAll.mockRejectedValue(new Error('Database error'));

            const response = await request(app)
                .get('/api/genres')
                .expect(500);

            expect(response.body).toEqual({
                error_code: 'server_error',
                error_description: 'A server error occurred'
            });
        });
    });

    describe('GET /api/genrechannels', () => {
        it('should return channels for a specific genre', async () => {
            const mockGenreWithChannels = {
                id: 1,
                name: 'Rock',
                description: 'Rock music',
                image: 'rock.jpg',
                path: '/rock',
                genreChannels: [{
                    order: 1,
                    channel: {
                        id: 1,
                        name: 'Classic Rock',
                        description: 'Classic rock hits',
                        image: 'classic-rock.jpg'
                    }
                }]
            };

            db.models.genre.findOne.mockResolvedValue(mockGenreWithChannels);

            const response = await request(app)
                .get('/api/genrechannels')
                .query({ genreId: 1 })
                .expect(200);

            expect(response.body).toEqual({
                genre: {
                    id: 1,
                    name: 'Rock',
                    description: 'Rock music',
                    image: 'rock.jpg',
                    path: '/rock'
                },
                channels: [{
                    id: 1,
                    name: 'Classic Rock',
                    description: 'Classic rock hits',
                    image: 'classic-rock.jpg',
                    order: 1
                }]
            });
        });

        it('should return 400 when genre ID is missing', async () => {
            const response = await request(app)
                .get('/api/genrechannels')
                .expect(400);

            expect(response.body).toEqual({
                error_code: 'invalid_request',
                error_description: 'Genre ID is required'
            });
        });
    });

    describe('Health Check', () => {
        it('should return 200 OK', async () => {
            await request(app)
                .get('/health')
                .expect(200);
        });
    });
});
