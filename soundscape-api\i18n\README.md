# Internationalization (i18n) API

The i18n API provides endpoints for managing translations and language support across the Southern Soundscape platform.

## Setup

1. Install dependencies:
```bash
cd soundscape-api/i18n
npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
```

Required environment variables:
```bash
NODE_ENV=development
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=soundscape
DB_USER=ss
DB_PASSWORD=password
```

3. Start the service:
```bash
npm start        # Production mode
npm run dev      # Development mode
```

## Testing

```bash
npm test                # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Generate coverage report
```

## Docker Support

Build and run using Docker:
```bash
# Build the container
./build-dev.sh     # Development environment
./build-prod.sh    # Production environment

# Run with Docker Compose
docker-compose up i18n-api
```

## API Endpoints

### Translations

#### GET /api/i18n/:lang/:namespace
Get translations for a specific language and namespace.

Response:
```json
{
  "namespace.key1": "Translated value 1",
  "namespace.key2": "Translated value 2"
}
```

#### GET /api/i18n/:lang
Get all translations for a language.

Response:
```json
{
  "common": {
    "key1": "value1"
  },
  "errors": {
    "key2": "value2"
  }
}
```

#### PUT /api/i18n/:lang/:namespace
Update translations for a specific language and namespace.

Request body:
```json
{
  "translations": {
    "key1": "value1",
    "key2": "value2"
  }
}
```

#### POST /api/i18n/:lang/:namespace/keys
Add new translation key.

Request body:
```json
{
  "key": "new.translation.key",
  "value": "Translated text"
}
```

#### DELETE /api/i18n/:lang/:namespace/keys/:key
Delete translation key.

### Languages

#### GET /api/i18n/languages
Get list of supported languages.

Response:
```json
[
  {
    "code": "en",
    "nativeName": "English",
    "isRTL": false,
    "isEnabled": true
  }
]
```

#### POST /api/i18n/languages
Add support for new language.

Request body:
```json
{
  "language": "es",
  "nativeName": "Español",
  "isRTL": false
}
```

#### PUT /api/i18n/languages/:lang
Update language settings.

Request body:
```json
{
  "nativeName": "Español",
  "isRTL": false,
  "isEnabled": true
}
```

#### DELETE /api/i18n/languages/:lang
Delete language support.

## Error Responses

All endpoints return standard error responses:

```json
{
  "error_code": "string",
  "error_description": "string"
}
```

Common error codes:
- 400: Bad Request
- 404: Language or Namespace Not Found
- 409: Conflict (duplicate key)
- 500: Server Error

## Project Structure

```
i18n/
├── app.js              # Application entry point
├── routes/            
│   ├── translations.js # Translation endpoints
│   └── languages.js    # Language management endpoints
├── services/           # Business logic
├── models/             # Data models
└── tests/             # Unit tests
```

## Contributing

1. Create a feature branch
2. Make changes and add tests
3. Run tests and ensure they pass
4. Submit pull request

## License

ISC
