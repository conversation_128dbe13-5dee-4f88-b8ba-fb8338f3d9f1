const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const i18next = require('i18next');
const i18nextMiddleware = require('i18next-http-middleware');
const i18nextBackend = require('i18next-fs-backend');
const { connection } = require('./src/database');

const app = express();
const port = process.env.PORT || 4007;

// Middleware
app.use(cors());
app.use(express.json());
app.use(cookieParser());

// i18n setup
i18next
  .use(i18nextBackend)
  .use(i18nextMiddleware.LanguageDetector)
  .init({
    backend: {
      loadPath: './locales/{{lng}}/{{ns}}.json',
    },
    fallbackLng: 'en',
    preload: ['en', 'fr', 'ja'],
    ns: ['common', 'errors', 'music'],
    defaultNS: 'common'
  });

app.use(i18nextMiddleware.handle(i18next));

// Routes
app.use('/api/i18n', require('./src/routes/translations'));
app.use('/api/i18n/languages', require('./src/routes/languages'));

// Error handling
app.use((err, req, res, next) => {
  res.status(err.status || 500).json({
    error_code: err.code || 'INTERNAL_ERROR',
    error_description: err.message
  });
});

const initServer = async () => {
  try {
    await connection.authenticate();
    console.log('Database connection established successfully.');

    app.listen(port, () => {
      console.log(`I18n API listening on port ${port}`);
    });
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
};

initServer();
