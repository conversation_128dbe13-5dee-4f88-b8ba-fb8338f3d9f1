{"name": "ss-i18n-api", "version": "1.0.0", "description": "Southern Soundscape Internationalization API", "main": "app.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "author": "", "license": "ISC", "dependencies": {"cookie-parser": "^1.4.5", "cors": "^2.8.5", "express": "^4.17.1", "fs": "^0.0.1-security", "http-errors": "^2.0.0", "node-cache": "^5.1.2", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3", "i18next": "^23.8.2", "i18next-fs-backend": "^2.3.1", "i18next-http-middleware": "^3.5.0"}}