const express = require('express');
const router = express.Router();

// Get list of supported languages
router.get('/', async (req, res) => {
  const languages = await getSupportedLanguages();
  res.json(languages);
});

// Add support for new language
router.post('/', async (req, res) => {
  const { language, nativeName, isRTL } = req.body;
  await addLanguage(language, nativeName, isRTL);
  res.status(201).send();
});

// Update language settings
router.put('/:lang', async (req, res) => {
  const { lang } = req.params;
  const { nativeName, isRTL, isEnabled } = req.body;
  await updateLanguage(lang, { nativeName, isRTL, isEnabled });
  res.status(200).send();
});

// Delete language support
router.delete('/:lang', async (req, res) => {
  const { lang } = req.params;
  await deleteLanguage(lang);
  res.status(204).send();
});

module.exports = router;