const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const i18next = require('i18next');
const i18nextMiddleware = require('i18next-http-middleware');
const i18nextBackend = require('i18next-fs-backend');
const errorHandler = require('../../shared/middleware/errorHandler');

const translationsRouter = require('./routes/translations');
const languagesRouter = require('./routes/languages');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(cookieParser());

// Health check
app.get('/health', (req, res) => res.sendStatus(200));

// i18n setup
i18next
    .use(i18nextBackend)
    .use(i18nextMiddleware.LanguageDetector)
    .init({
        backend: {
            loadPath: './locales/{{lng}}/{{ns}}.json',
        },
        fallbackLng: 'en',
        preload: ['en', 'fr', 'ja'],
        ns: ['common', 'errors', 'music'],
        defaultNS: 'common'
    });

app.use(i18nextMiddleware.handle(i18next));

// Routes
app.use('/api/i18n', translationsRouter);
app.use('/api/i18n/languages', languagesRouter);

// Error handling
app.use(errorHandler);

module.exports = app;
