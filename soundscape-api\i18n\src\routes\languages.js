const express = require('express');
const router = express.Router();
const LanguageService = require('../services/languageService');

const languageService = new LanguageService();

// Get list of supported languages
router.get('/', async (req, res) => {
    const languages = await languageService.getSupportedLanguages();
    res.json(languages);
});

// Add support for new language
router.post('/', async (req, res) => {
    const { language, nativeName, isRTL } = req.body;
    await languageService.addLanguage(language, nativeName, isRTL);
    res.status(201).send();
});

// Update language settings
router.put('/:lang', async (req, res) => {
    const { lang } = req.params;
    const { nativeName, isRTL, isEnabled } = req.body;
    await languageService.updateLanguage(lang, { nativeName, isRTL, isEnabled });
    res.status(200).send();
});

// Delete language support
router.delete('/:lang', async (req, res) => {
    const { lang } = req.params;
    await languageService.deleteLanguage(lang);
    res.status(204).send();
});

module.exports = router;