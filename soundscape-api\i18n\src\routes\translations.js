const express = require('express');
const router = express.Router();
const I18nService = require('../services/i18nService');

const i18nService = new I18nService();

// Get translations for a specific language and namespace
router.get('/:lang/:namespace', async (req, res) => {
    const { lang, namespace } = req.params;
    const translations = await i18nService.loadTranslations(lang, namespace);
    res.json(translations);
});

// Get all translations for a language
router.get('/:lang', async (req, res) => {
    const { lang } = req.params;
    const translations = await i18nService.loadAllTranslations(lang);
    res.json(translations);
});

// Update translations for a specific language and namespace
router.put('/:lang/:namespace', async (req, res) => {
    const { lang, namespace } = req.params;
    const { translations } = req.body;
    await i18nService.updateTranslations(lang, namespace, translations);
    res.status(200).send();
});

// Add new translation key
router.post('/:lang/:namespace/keys', async (req, res) => {
    const { lang, namespace } = req.params;
    const { key, value } = req.body;
    await i18nService.addTranslationKey(lang, namespace, key, value);
    res.status(201).send();
});

// Delete translation key
router.delete('/:lang/:namespace/keys/:key', async (req, res) => {
    const { lang, namespace, key } = req.params;
    await i18nService.deleteTranslationKey(lang, namespace, key);
    res.status(204).send();
});

module.exports = router;