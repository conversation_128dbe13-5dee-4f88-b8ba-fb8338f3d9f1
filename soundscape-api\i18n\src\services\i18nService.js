const i18next = require('i18next');
const NodeCache = require('node-cache');
const db = require('../database');

// Cache for translations (1 hour)
const cache = new NodeCache({ stdTTL: 3600 });

class I18nService {
    constructor() {
        this.cache = cache;
    }

    async loadTranslations(lang, namespace) {
        const cacheKey = `translations_${lang}_${namespace}`;
        let translations = cache.get(cacheKey);

        if (!translations) {
            translations = await db.query(
                'SELECT key, value FROM translations WHERE language = ? AND namespace = ?',
                [lang, namespace]
            );
            cache.set(cacheKey, translations);
        }

        return translations;
    }

    async loadAllTranslations(lang) {
        const cacheKey = `all_translations_${lang}`;
        let translations = cache.get(cacheKey);

        if (!translations) {
            translations = await db.query(
                'SELECT namespace, key, value FROM translations WHERE language = ?',
                [lang]
            );
            cache.set(cacheKey, translations);
        }

        return this.groupByNamespace(translations);
    }

    async updateTranslations(lang, namespace, translations) {
        await db.transaction(async (transaction) => {
            for (const [key, value] of Object.entries(translations)) {
                await db.query(
                    'UPDATE translations SET value = ? WHERE language = ? AND namespace = ? AND key = ?',
                    [value, lang, namespace, key],
                    { transaction }
                );
            }
        });

        cache.del(`translations_${lang}_${namespace}`);
        cache.del(`all_translations_${lang}`);
    }

    async addTranslationKey(lang, namespace, key, value) {
        await db.query(
            'INSERT INTO translations (language, namespace, key, value) VALUES (?, ?, ?, ?)',
            [lang, namespace, key, value]
        );

        cache.del(`translations_${lang}_${namespace}`);
        cache.del(`all_translations_${lang}`);
    }

    async deleteTranslationKey(lang, namespace, key) {
        await db.query(
            'DELETE FROM translations WHERE language = ? AND namespace = ? AND key = ?',
            [lang, namespace, key]
        );

        cache.del(`translations_${lang}_${namespace}`);
        cache.del(`all_translations_${lang}`);
    }

    private groupByNamespace(translations) {
        return translations.reduce((acc, { namespace, key, value }) => {
            acc[namespace] = acc[namespace] || {};
            acc[namespace][key] = value;
            return acc;
        }, {});
    }
}

module.exports = I18nService;