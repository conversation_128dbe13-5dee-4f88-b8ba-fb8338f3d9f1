const NodeCache = require('node-cache');
const Language = require('../models/language');

// Cache for languages (24 hours)
const cache = new NodeCache({ stdTTL: 86400 });

class LanguageService {
    constructor() {
        this.cache = cache;
    }

    async getSupportedLanguages() {
        const cacheKey = 'supported_languages';
        let languages = cache.get(cacheKey);

        if (!languages) {
            languages = await Language.findAll({
                attributes: ['code', 'native_name', 'is_rtl', 'is_enabled']
            });
            cache.set(cacheKey, languages);
        }

        return languages;
    }

    async addLanguage(code, nativeName, isRTL) {
        await Language.create({
            code,
            native_name: nativeName,
            is_rtl: isRTL,
            is_enabled: true
        });
        cache.del('supported_languages');
    }

    async updateLanguage(code, { nativeName, isRTL, isEnabled }) {
        await Language.update({
            native_name: nativeName,
            is_rtl: isRTL,
            is_enabled: isEnabled
        }, {
            where: { code }
        });
        cache.del('supported_languages');
    }

    async deleteLanguage(code) {
        await Language.destroy({
            where: { code }
        });
        cache.del('supported_languages');
    }

    async getLanguage(code) {
        return await Language.findByPk(code);
    }
}

module.exports = LanguageService;
