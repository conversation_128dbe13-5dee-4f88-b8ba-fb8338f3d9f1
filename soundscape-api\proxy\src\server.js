const http = require('http');
const { handleRequest } = require('./handler');
const config = require('./config');
const logger = require('./utils/logger');

const server = http.createServer(async (req, res) => {
    try {
        await handleRequest(req, res);
    } catch (error) {
        logger.error('Request handler error:', error);
        handleError(res, error);
    }
});

function handleError(res, error) {
    try {
        res.statusCode = 500;
        res.setHeader('Content-Type', 'text/plain');

        const message = config.isDevelopment ? error.message : '';
        res.end(message);
    } catch (err) {
        logger.error('Error handler failed:', err);
    }
}

server.listen(config.port, config.hostname, () => {
    logger.info(`Server running at http://${config.hostname}:${config.port}/`);
});

process.on('unhandledRejection', (error) => {
    logger.error('Unhandled rejection:', error);
});

process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', error);
    process.exit(1);
});
