const https = require('https');
const getClientIp = require('request-ip').getClientIp;

class ProxyService {
    constructor() {
        this._host = process.env.THIS_HOST || "https://proxy.southernsoundscape.com";
        this._cdn = this._host + "/cdn-cf";
        this._adServer = process.env.AD_SERVER;
        this._adCdn = process.env.AD_CDN;

        this.adServerRegex = new RegExp(this._adServer, 'ig');
        this.adCDNRegex = new RegExp(this._adCdn, 'ig');
        
        this.replacements = {
            companion: {
                k: "300x250_ad",
                v: "3__x_25___"
            }
        };
    }

    async handleRequest(req, res) {
        res = this._setCORSOptions(req, res);
        
        if (this._shouldSkipForwarding(req)) {
            res.statusCode = 200;
            res.end();
            return;
        }
        
        const response = await this._forwardRequestToAdServer(req);
        await this._respondToClient(res, response);
    }

    _setCORSOptions(req, res) {
        const rx = RegExp('((?:.+\.)?southernsoundscape\.(com|local|internal)(?::\d{1,5})?)$');
        const origin = rx.test(req.headers.host) ? "*" : this._host;
        
        res.setHeader('Access-Control-Allow-Origin', origin);
        res.setHeader('Access-Control-Allow-Methods', 'OPTIONS, GET');
        res.setHeader('Access-Control-Allow-Headers', '*');
        return res;
    }

    _shouldSkipForwarding(req) {
        return req.url.indexOf("/favicon.ico") >= 0 ||
               req.url.indexOf("/health-check") >= 0 ||
               req.url.length < 10 ||
               req.method !== 'GET';
    }

    async _forwardRequestToAdServer(req) {
        const response = await this._makeAdServerRequest(req);
        return this._processAdServerResponse(response);
    }

    async _makeAdServerRequest(req) {
        return new Promise((resolve, reject) => {
            const adServerUrl = this._getAdServerUrl(req.url);
            const options = {
                headers: {
                    "X-Forwarded-For": getClientIp(req),
                    "User-Agent": req.headers["user-agent"] || "NA"
                }
            };

            https.get(adServerUrl, options, (response) => {
                const encoding = this._getEncoding(response.headers["content-type"]);
                response.setEncoding(encoding);
                resolve({ response, encoding });
            }).on("error", reject);
        });
    }

    async _processAdServerResponse({ response, encoding }) {
        return new Promise((resolve, reject) => {
            let data = '';
            response.on('data', chunk => data += chunk);
            response.on('error', reject);
            response.on('end', () => {
                if (encoding === "utf-8") {
                    data = this._replaceResponseTokens(data);
                }
                resolve({ data, response, encoding });
            });
        });
    }

    async _respondToClient(res, { data, response, encoding }) {
        res.statusCode = response.statusCode;
        res.setHeader('Content-Type', response.headers['content-type'] || 'text/plain');
        res.end(data, encoding);
    }

    _getAdServerUrl(incomingUrl) {
        const isImpressionOrTracking = incomingUrl?.indexOf("/vast/4.0") >= 0;
        const isCDN = incomingUrl.indexOf("/cdn-cf") >= 0;

        let url = isImpressionOrTracking ? (this._adServer + incomingUrl) :
                 isCDN ? incomingUrl.replace("/cdn-cf", this._adCdn) :
                 '';

        return this._replaceUrlTokens(url);
    }

    _replaceResponseTokens(data) {
        return data
            .replace(this.adServerRegex, this._host)
            .replace(this.adCDNRegex, this._cdn)
            .replace(this.replacements.companion.k, this.replacements.companion.v);
    }

    _replaceUrlTokens(url) {
        return url.replace(this.replacements.companion.v, this.replacements.companion.k);
    }

    _getEncoding(contentType) {
        switch (contentType) {
            case "image/jpeg":
            case "audio/mpeg":
                return "binary";
            default:
                return "utf-8";
        }
    }
}

module.exports = new ProxyService();