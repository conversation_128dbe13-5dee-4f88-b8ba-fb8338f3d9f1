#!/bin/bash

# Source AWS configuration
source $(dirname "$0")/../config/aws-config.sh

# Required parameters
SERVICE_NAME=$1
ENV=$2

if [ -z "$SERVICE_NAME" ] || [ -z "$ENV" ]; then
    echo "Usage: $0 <service-name> <environment>"
    echo "Example: $0 usage prod"
    exit 1
fi

# Set image tags based on environment
if [ "$ENV" == "prod" ]; then
    LOCAL_TAG="ss/${SERVICE_NAME}-api:latest"
    ECR_TAG="${AWS_ECR_URL}/ss/${SERVICE_NAME}-api:latest"
else
    LOCAL_TAG="ss/${SERVICE_NAME}-api-dev:latest"
    ECR_TAG="${AWS_ECR_URL}/ss/${SERVICE_NAME}-api-dev:latest"
fi

# Build docker image
echo "Building Docker image: ${LOCAL_TAG}"
docker build --no-cache -t ${LOCAL_TAG} .

# Login to AWS ECR
echo "Logging into AWS ECR..."
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ECR_URL}

# Tag and push to ECR
echo "Tagging and pushing to ECR: ${ECR_TAG}"
docker tag ${LOCAL_TAG} ${ECR_TAG}
docker push ${ECR_TAG}