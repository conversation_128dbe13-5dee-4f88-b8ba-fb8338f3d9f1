# Search API

The Search API provides powerful search capabilities across the Southern Soundscape platform, including tracks, artists, albums, and playlists.

## API Endpoints

### Search

#### GET /api/search
Perform a global search across all content types.

Query parameters:
- `q`: Search query string
- `type`: Comma-separated list of types (track,artist,album,playlist)
- `limit`: Maximum number of results per type (default: 20)
- `offset`: Pagination offset (default: 0)

Response:
```json
{
  "tracks": [
    {
      "id": "string",
      "title": "string",
      "artistId": "string",
      "artistName": "string",
      "albumId": "string",
      "albumName": "string",
      "duration": number
    }
  ],
  "artists": [...],
  "albums": [...],
  "playlists": [...]
}
```

#### GET /api/search/tracks
Search tracks only.

#### GET /api/search/artists
Search artists only.

#### GET /api/search/albums
Search albums only.

#### GET /api/search/playlists
Search playlists only.

### Advanced Search

#### POST /api/search/advanced
Perform advanced search with filters.

Request body:
```json
{
  "query": "string",
  "filters": {
    "genre": ["string"],
    "year": {
      "from": number,
      "to": number
    },
    "duration": {
      "min": number,
      "max": number
    }
  },
  "sort": {
    "field": "string",
    "order": "asc|desc"
  }
}
```

## Error Responses

All endpoints return standard error responses:

```json
{
  "error_code": "string",
  "error_description": "string"
}
```

Common error codes:
- 400: Invalid Search Parameters
- 429: Rate Limit Exceeded
- 500: Server Error
