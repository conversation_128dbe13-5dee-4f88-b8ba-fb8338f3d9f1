const express = require('express')

const app = express()
const port = process.env.PORT || 4003
const bodyParser = require('body-parser')
const cookieParser = require('cookie-parser')
const setRoutes = require('./src/routes');
const cors = require('cors')


const initServer = async () => {
    app.use(bodyParser.urlencoded({ extended: true }));
    app.use(bodyParser.json());
    app.use(cors());
    app.use(cookieParser());

    setRoutes(app)

    app.listen(port);
    console.log('Search API server started on: ' + port);
}

initServer();





