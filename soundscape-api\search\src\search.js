const SearchService = require('./services/searchService');

module.exports = {
    search: async function (req, res, next) {
        try {
            const search = new SearchService(req);
            const result = await search.search();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    getGenreChannels: async function (req, res, next) {
        try {

            const search = new SearchService(req);
            let result = await search.getGenreChannels();
            res.json(result);

        } catch (e) {
            return next(e);
        }
    },

    getGenres: async function (req, res, next) {
        try {
            const search = new SearchService(req);
            let result = await search.getGenres();
            res.json(result);
        } catch (e) {
            console.log(next(e))
            return next(e);
        }
    },
};
