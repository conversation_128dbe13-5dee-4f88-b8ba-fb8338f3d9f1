const db = require('../database');
const { Op } = require('sequelize');

class SearchService {
    constructor(req) {
        this.searchQuery = req.query.query?.trim().toLowerCase() || '';
        this.limit = parseInt(req.query.limit) || 20;
        this.offset = parseInt(req.query.offset) || 0;
    }

    async search() {
        if (!this.searchQuery) {
            return {
                channels: [],
                total: 0
            };
        }

        // Find channels that match the search criteria
        const channels = await db.models.channel.findAndCountAll({
            where: {
                is_active: true,
                is_public: true
            },
            include: [
                {
                    model: db.models.channel_track,
                    include: [
                        {
                            model: db.models.track,
                            include: [
                                {
                                    model: db.models.track_artist,
                                    include: [
                                        {
                                            model: db.models.artist,
                                            where: {
                                                [Op.or]: [
                                                    {
                                                        name: {
                                                            [Op.iLike]: `%${this.searchQuery}%`
                                                        }
                                                    }
                                                ]
                                            },
                                            required: false
                                        }
                                    ]
                                },
                                {
                                    model: db.models.album,
                                    where: {
                                        [Op.or]: [
                                            {
                                                title: {
                                                    [Op.iLike]: `%${this.searchQuery}%`
                                                }
                                            }
                                        ]
                                    },
                                    required: false
                                }
                            ],
                            where: {
                                [Op.or]: [
                                    {
                                        title: {
                                            [Op.iLike]: `%${this.searchQuery}%`
                                        }
                                    }
                                ]
                            },
                            required: false
                        }
                    ]
                }
            ],
            having: db.Sequelize.literal(`
                (
                    LOWER(channel.name) LIKE '%${this.searchQuery}%' OR
                    LOWER(channel.description) LIKE '%${this.searchQuery}%' OR
                    EXISTS (
                        SELECT 1 FROM channel_tracks ct
                        JOIN tracks t ON t.id = ct.track_id
                        LEFT JOIN track_artists ta ON ta.track_id = t.id
                        LEFT JOIN artists a ON a.id = ta.artist_id
                        LEFT JOIN albums al ON al.id = t.album_id
                        WHERE ct.channel_id = channel.id
                        AND (
                            LOWER(t.title) LIKE '%${this.searchQuery}%' OR
                            LOWER(a.name) LIKE '%${this.searchQuery}%' OR
                            LOWER(al.title) LIKE '%${this.searchQuery}%'
                        )
                    )
                )
            `),
            limit: this.limit,
            offset: this.offset,
            distinct: true,
            order: [
                ['name', 'ASC']
            ]
        });

        // Transform the results
        const transformedChannels = channels.rows.map(channel => ({
            id: channel.id,
            name: channel.name,
            description: channel.description,
            image: channel.image,
            matchedContent: this.getMatchedContent(channel)
        }));

        return {
            channels: transformedChannels,
            total: channels.count,
            limit: this.limit,
            offset: this.offset
        };
    }

    getMatchedContent(channel) {
        const matches = {
            tracks: new Set(),
            artists: new Set(),
            albums: new Set()
        };

        channel.channel_tracks?.forEach(ct => {
            const track = ct.track;
            if (!track) return;

            // Check if track title matches
            if (track.title.toLowerCase().includes(this.searchQuery)) {
                matches.tracks.add(track.title);
            }

            // Check if album matches
            if (track.album?.title.toLowerCase().includes(this.searchQuery)) {
                matches.albums.add(track.album.title);
            }

            // Check if any artists match
            track.track_artists?.forEach(ta => {
                if (ta.artist?.name.toLowerCase().includes(this.searchQuery)) {
                    matches.artists.add(ta.artist.name);
                }
            });
        });

        return {
            tracks: Array.from(matches.tracks),
            artists: Array.from(matches.artists),
            albums: Array.from(matches.albums)
        };
    }
}

module.exports = SearchService;
