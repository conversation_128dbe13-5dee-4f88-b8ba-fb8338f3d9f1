'use strict';

const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');
const { ENV_FILE } = require('./paths');

// Load environment variables from a single, well-defined location
if (fs.existsSync(ENV_FILE)) {
    dotenv.config({ path: ENV_FILE });
} else {
    console.warn('No .env file found at', ENV_FILE, 'using default values');
}

const Sequelize = require('sequelize');

const dbConfig = {
    user: process.env.DB_USER || 'ss',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'soundscape',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432
};

function createConnection(modelsPath) {
    console.log("Starting database setup...");
    console.log("Using database config:", {
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        username: dbConfig.user
    });

    const connection = new Sequelize({
        dialect: 'postgres',
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        username: dbConfig.user,
        password: dbConfig.password,
        define: {
            timestamps: false
        }
    });

    // Load and initialize models
    const models = {};

    fs.readdirSync(modelsPath)
        .filter(file => file.indexOf('.') !== 0 && file.slice(-3) === '.js' && file !== 'index.js')
        .forEach(file => {
            console.log(`Loading model file: ${file}`);
            const model = require(path.join(modelsPath, file))(connection, Sequelize.DataTypes);
            console.log(`Model loaded: ${model.name}`);
            models[model.name] = model;
        });

    // Set up associations after all models are loaded
    Object.keys(models).forEach(modelName => {
        if (models[modelName].associate) {
            console.log(`Setting up associations for model: ${modelName}`);
            models[modelName].associate(models);
        }
    });

    return { connection, models };
}

module.exports = {
    dbConfig,
    createConnection
};
