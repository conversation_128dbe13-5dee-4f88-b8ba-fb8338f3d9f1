const axios = require('axios');
const NodeCache = require('node-cache');

// Cache user info for 1 hour (3600 seconds)
const userCache = new NodeCache({ stdTTL: 3600 });

class AuthMiddleware {
    static async authenticate(req, res, next) {
        try {
            const authHeader = req.headers.authorization;

            // If no auth header, treat as anonymous user
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                req.user = {
                    isAnonymous: true,
                    // Generate a temporary session ID for anonymous users
                    sessionId: `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                };
                return next();
            }

            // Handle authenticated users
            const token = authHeader.split(' ')[1];
            const userInfo = await AuthMiddleware.getUserInfo(token);

            if (!userInfo || !userInfo.email) {
                return res.status(401).json({
                    error_code: 'unauthorized',
                    error_description: 'Invalid or expired token'
                });
            }

            // Attach authenticated user info to request object
            req.user = {
                isAnonymous: false,
                email: userInfo.email,
                id: userInfo.sub,
                name: userInfo.name
            };

            next();
        } catch (error) {
            console.error('Authentication error:', error);

            if (error.response && error.response.status === 401) {
                return res.status(401).json({
                    error_code: 'unauthorized',
                    error_description: 'Invalid or expired token'
                });
            }

            return res.status(500).json({
                error_code: 'server_error',
                error_description: 'Authentication service error'
            });
        }
    }

    static async getUserInfo(token) {
        // Check cache first
        const cachedUser = userCache.get(token);
        if (cachedUser) {
            return cachedUser;
        }

        // If not in cache, fetch from OpenPass
        const response = await axios.get('https://auth.myopenpass.com/v1/api/userinfo', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        const userInfo = response.data;

        // Cache the user info
        userCache.set(token, userInfo);

        return userInfo;
    }
}

module.exports = AuthMiddleware;
