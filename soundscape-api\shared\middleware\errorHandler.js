const createError = require('http-errors');

const errorHandler = (err, req, res, next) => {
    // Handle Sequelize-specific errors
    if (err.name === 'SequelizeValidationError') {
        return res.status(400).json({
            error_code: 'validation_error',
            error_description: err.message,
            errors: err.errors
        });
    }

    if (err.name === 'SequelizeUniqueConstraintError') {
        return res.status(409).json({
            error_code: 'conflict',
            error_description: 'Resource already exists'
        });
    }

    // Handle custom error types (like those from http-errors)
    if (err.status && err.error_code) {
        return res.status(err.status).json({
            error_code: err.error_code,
            error_description: err.error_description
        });
    }

    // Log unexpected errors
    console.error('Unexpected error:', err);

    // Return generic error for unknown cases
    res.status(500).json({
        error_code: 'server_error',
        error_description: process.env.NODE_ENV === 'production' 
            ? 'An unexpected error occurred' 
            : err.message
    });
};

module.exports = errorHandler;