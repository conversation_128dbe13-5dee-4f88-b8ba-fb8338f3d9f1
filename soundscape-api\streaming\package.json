{"name": "ss-streaming-api", "version": "1.0.0", "description": "Southern Soundscape Streaming API", "main": "app.js", "scripts": {"start": ". /etc/environment; node app.js", "dev": "node app.js", "test": "NODE_ENV=test jest --setupFiles dotenv/config", "test:watch": "NODE_ENV=test jest --watch --setupFiles dotenv/config", "test:coverage": "NODE_ENV=test jest --coverage --setupFiles dotenv/config"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.779.0", "axios": "^1.8.4", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "express": "^4.17.1", "fs": "^0.0.1-security", "http-errors": "^2.0.0", "node-cache": "^5.1.2", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"], "testMatch": ["**/tests/**/*.test.js"], "setupFiles": ["<rootDir>/tests/setup.js"]}}