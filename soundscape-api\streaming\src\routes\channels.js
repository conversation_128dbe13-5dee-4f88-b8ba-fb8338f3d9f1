const express = require('express');
const router = express.Router();
const { models } = require('../database');

router.get('/:channelId/tracks', async (req, res) => {
  try {
    const { channelId } = req.params;

    // Fetch channel info with basic track information
    const channel = await models.channel.findOne({
      where: { id: channelId },
      include: [{
        model: models.channel_track,
        as: 'channelTracks',
        include: [{
          model: models.track,
          as: 'track',
          include: [{
            model: models.album,
            as: 'album'
          }]
        }]
      }]
    });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    // Transform the data to match the frontend needs
    const tracks = channel.channelTracks.map(ct => ({
      id: ct.track.id,
      title: ct.track.title,
      url: ct.track.url,
      weight: ct.weight,
      albumId: ct.track.album_id,
      albumTitle: ct.track.album ? ct.track.album.title : 'Unknown Album',
      albumImage: ct.track.album ? ct.track.album.image : null,
      artists: [{
        id: 1,
        name: 'Unknown Artist',
        role: 'Primary'
      }], // Simplified for now
      duration: ct.track.duration_seconds
    }));

    // Return both channel info and tracks
    res.json({
      channelInfo: {
        id: channel.id,
        name: channel.name,
        description: channel.description,
        image: channel.image,
        isPublic: channel.is_public,
        isActive: channel.is_active
      },
      tracks
    });

  } catch (error) {
    console.error('Error fetching channel tracks:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;