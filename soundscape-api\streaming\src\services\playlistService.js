const db = require('../database');
const NodeCache = require('node-cache');

// Cache for 24 hours (86400 seconds)
const cache = new NodeCache({ stdTTL: 86400 });

class PlaylistService {
    constructor(user, channelId) {
        this.user = user;
        this.channelId = channelId;
    }

    async getOrCreatePlaylist() {
        const cacheKey = this.getCacheKey();
        let playlist = cache.get(cacheKey);

        if (!playlist) {
            const channelTracks = await this.fetchChannelTracks();
            playlist = this.generateWeightedPlaylist(channelTracks);
            playlist = this.injectAds(playlist);

            // Cache with appropriate TTL
            const ttl = this.user.isAnonymous ? 3600 : 86400; // 1 hour for anonymous, 24 hours for authenticated
            cache.set(cacheKey, playlist, ttl);
        }

        return playlist;
    }

    getCacheKey() {
        return this.user.isAnonymous
            ? `playlist_anon_${this.channelId}_${this.user.sessionId}`
            : `playlist_${this.user.email}_${this.channelId}`;
    }

    async fetchChannelTracks() {
        if (this.user.isAnonymous) {
            return await db.models.channel_track.findAll({
                where: {
                    channel_id: this.channelId
                },
                include: [{
                    model: db.models.track,
                    as: 'track',
                    where: { is_active: true }
                }]
            });
        }

        const bannedSongs = await db.models.user_banned_tracks.findAll({
            where: { user_email: this.user.email }
        });

        return await db.models.channel_track.findAll({
            where: {
                channel_id: this.channelId,
                track_id: {
                    [db.Sequelize.Op.notIn]: bannedSongs.map(b => b.track_id)
                }
            },
            include: [{
                model: db.models.track,
                as: 'track',
                where: { is_active: true }
            }]
        });
    }

    generateWeightedPlaylist(channelTracks) {
        const weightedTracks = [];

        // Apply weighting factors
        channelTracks.forEach(ct => {
            const track = ct.track;
            const baseWeight = ct.weight || 1;

            // Calculate final weight based on various factors
            const finalWeight = this.calculateTrackWeight(track, baseWeight);

            // Add track to array multiple times based on weight
            for (let i = 0; i < finalWeight; i++) {
                weightedTracks.push(track);
            }
        });

        // Shuffle and take 40 tracks
        return this.shuffleArray(weightedTracks).slice(0, 40);
    }

    calculateTrackWeight(track, baseWeight) {
        let weight = baseWeight;

        // Boost weight based on track popularity
        if (track.play_count) {
            weight *= (1 + Math.log10(track.play_count) * 0.1);
        }

        // Reduce weight for recently played tracks
        if (track.last_played) {
            const daysSinceLastPlayed = this.getDaysSince(track.last_played);
            if (daysSinceLastPlayed < 7) {
                weight *= (0.5 + (daysSinceLastPlayed / 14));
            }
        }

        // Round to nearest integer
        return Math.round(weight);
    }

    getDaysSince(date) {
        const diffTime = Math.abs(new Date() - new Date(date));
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    injectAds(playlist) {
        const withAds = [...playlist];
        for (let i = 4; i < withAds.length; i += 5) {
            withAds.splice(i, 0, {
                id: `ad_${Math.floor(Math.random() * 1000)}`,
                type: 'advertisement',
                bucket: process.env.AD_BUCKET,
                file_key: this.selectRandomAd()
            });
        }
        return withAds;
    }

    selectRandomAd() {
        // TODO: Implement more sophisticated ad selection logic
        const ads = ['ad1.mp3', 'ad2.mp3', 'ad3.mp3'];
        return ads[Math.floor(Math.random() * ads.length)];
    }
}

module.exports = PlaylistService;