const db = require('../database');
const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
const NodeCache = require('node-cache');
const PlaylistService = require('./playlistService');

// Cache for file metadata (24 hours)
const cache = new NodeCache({ stdTTL: 86400 });

class StreamingService {
    constructor(req, res) {
        this.req = req;
        this.res = res;
        this.channelId = req.params.channelId;
        this.user = req.user;
        this.range = req.headers.range;

        this.s3Client = new S3Client({
            region: process.env.AWS_REGION,
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
            }
        });

        // For backward compatibility
        this.s3 = {
            headObject: ({ Bucket, Key }) => ({
                promise: async () => {
                    try {
                        const command = new GetObjectCommand({ Bucket, Key });
                        const response = await this.s3Client.send(command);
                        return {
                            ContentLength: response.ContentLength
                        };
                    } catch (error) {
                        console.error('Error in headObject:', error);
                        throw error;
                    }
                }
            }),
            getObject: ({ Bucket, Key, Range }) => ({
                createReadStream: () => {
                    // This is a simplified implementation
                    // In a real implementation, you would create a proper stream
                    const command = new GetObjectCommand({ Bucket, Key, Range });
                    return this.s3Client.send(command).then(response => response.Body);
                }
            })
        };
    }

    async startStream() {
        try {
            if (!this.range) {
                return this.res.status(416).send('Requires Range header');
            }

            const playlistService = new PlaylistService(this.user, this.channelId);
            const playlist = await playlistService.getOrCreatePlaylist();
            const currentTrackIndex = parseInt(this.req.query.trackIndex) || 0;
            const currentTrack = playlist[currentTrackIndex];

            if (currentTrack.type === 'advertisement') {
                // Handle advertisement streaming (S3)
                const bucket = process.env.AD_BUCKET;
                const file_key = currentTrack.file_key;
                await this.streamFile(bucket, file_key, playlist.length, currentTrackIndex);
            } else {
                // Handle regular track streaming (direct URL)
                const metadata = await this.getFileMetadata(currentTrack.id);
                await this.streamDirectUrl(metadata.url, playlist.length, currentTrackIndex);
            }
        } catch (err) {
            console.error('Streaming error:', err);
            if (!this.res.headersSent) {
                this.res.status(500).send(err.message);
            }
        }
    }

    async streamFile(bucket, file_key, totalTracks, currentTrackIndex) {
        const headData = await this.s3.headObject({
            Bucket: bucket,
            Key: file_key
        }).promise();

        const fileSize = headData.ContentLength;
        const parts = this.range.replace(/bytes=/, '').split('-');
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunkSize = end - start + 1;

        if (start >= fileSize || end >= fileSize) {
            return this.res.status(416).send('Requested range not satisfiable');
        }

        const stream = this.s3.getObject({
            Bucket: bucket,
            Key: file_key,
            Range: `bytes=${start}-${end}`
        }).createReadStream();

        this.res.writeHead(206, {
            'Content-Range': `bytes ${start}-${end}/${fileSize}`,
            'Accept-Ranges': 'bytes',
            'Content-Length': chunkSize,
            'Content-Type': 'audio/mp3',
            'X-Track-Index': currentTrackIndex,
            'X-Total-Tracks': totalTracks
        });

        stream.pipe(this.res);

        stream.on('error', (err) => {
            console.error('Stream error:', err);
            if (!this.res.headersSent) {
                this.res.status(500).send('Error streaming file');
            }
        });
    }

    async streamDirectUrl(url, totalTracks, currentTrackIndex) {
        // For development: redirect to the direct URL
        // In production, you might want to proxy the stream
        if (!url) {
            return this.res.status(404).send('Track URL not found');
        }

        // For now, just redirect to the URL
        // This is a simple implementation for development
        this.res.writeHead(302, {
            'Location': url,
            'X-Track-Index': currentTrackIndex,
            'X-Total-Tracks': totalTracks
        });
        this.res.end();
    }

    async getFileMetadata(trackId) {
        const cacheKey = `track_${trackId}`;
        let metadata = cache.get(cacheKey);

        if (!metadata) {
            const track = await db.models.track.findByPk(trackId, {
                attributes: ['url', 'title']
            });
            if (!track) throw new Error('Track not found');

            metadata = {
                url: track.url,
                title: track.title
            };
            cache.set(cacheKey, metadata);
        }

        return metadata;
    }
}

module.exports = StreamingService;
