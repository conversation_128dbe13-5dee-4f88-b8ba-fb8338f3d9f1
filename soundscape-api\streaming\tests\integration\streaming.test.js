const request = require('supertest');
const express = require('express');
const app = express();

// Mock authentication middleware
jest.mock('../../src/middleware/auth', () => ({
    authenticateToken: (req, res, next) => {
        req.user = { email: '<EMAIL>' };
        next();
    }
}));

// Mock database
jest.mock('../../src/database', () => ({
    query: jest.fn().mockResolvedValue([{ bucket: 'test-bucket' }]),
    models: {
        channel_track: {
            findAll: jest.fn().mockResolvedValue([])
        }
    }
}));

// Import and setup routes
require('../../src/routes')(app);

describe('Streaming API Integration Tests', () => {
    describe('GET /health', () => {
        it('should return 200 OK', async () => {
            await request(app)
                .get('/health')
                .expect(200);
        });
    });

    describe('GET /api/stream/:channelId', () => {
        it('should require range header', async () => {
            await request(app)
                .get('/api/stream/123')
                .expect(416);
        });

        it('should accept valid range header', async () => {
            await request(app)
                .get('/api/stream/123')
                .set('Range', 'bytes=0-1000')
                .expect(206);
        });
    });
});
