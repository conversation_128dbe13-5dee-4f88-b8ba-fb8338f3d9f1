const PlaylistService = require('../../src/services/playlistService');

// Mock the database
jest.mock('../../src/database', () => ({
    models: {
        channel_track: {
            findAll: jest.fn().mockResolvedValue([
                {
                    track: {
                        id: '1',
                        title: 'Song 1',
                        play_count: 1000,
                        last_played: new Date(),
                        is_active: true,
                        bucket: 'test-bucket',
                        file_key: 'test.mp3'
                    },
                    weight: 2
                }
            ])
        },
        user_banned_tracks: {
            findAll: jest.fn().mockResolvedValue([])
        }
    },
    Sequelize: {
        Op: {
            notIn: Symbol('notIn')
        }
    }
}));

describe('PlaylistService', () => {
    let playlistService;
    const mockUser = { email: '<EMAIL>', isAnonymous: false };
    const channelId = '123';

    beforeEach(() => {
        jest.clearAllMocks();
        playlistService = new PlaylistService(mockUser, channelId);
    });

    describe('getOrCreatePlaylist', () => {
        it('should generate a playlist with tracks', async () => {
            const playlist = await playlistService.getOrCreatePlaylist();
            expect(playlist.length).toBeGreaterThan(0);
            expect(playlist[0]).toHaveProperty('id');
            expect(playlist[0]).toHaveProperty('type');
        });

        it('should exclude banned tracks for authenticated users', async () => {
            const bannedTrackIds = ['2', '3'];
            const db = require('../../src/database');
            db.models.user_banned_tracks.findAll.mockResolvedValue(
                bannedTrackIds.map(id => ({ track_id: id }))
            );

            await playlistService.getOrCreatePlaylist();

            expect(db.models.channel_track.findAll).toHaveBeenCalled();
        });
    });

    describe('calculateTrackWeight', () => {
        it('should calculate weight based on track properties', () => {
            const track = {
                play_count: 1000,
                last_played: new Date()
            };
            const weight = playlistService.calculateTrackWeight(track, 1);
            expect(typeof weight).toBe('number');
            expect(weight).toBeGreaterThanOrEqual(1);
        });
    });
});
