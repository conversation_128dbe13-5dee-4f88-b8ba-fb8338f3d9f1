const StreamingService = require('../../src/services/streamingService');
const PlaylistService = require('../../src/services/playlistService');

// Mock the database module
jest.mock('../../src/database', () => ({
    query: jest.fn().mockResolvedValue([{ bucket: 'test-bucket' }]),
    models: {
        channel_track: {
            findAll: jest.fn()
        }
    }
}));

// Mock AWS S3
jest.mock('aws-sdk', () => ({
    S3: jest.fn(() => ({
        headObject: jest.fn().mockReturnValue({
            promise: jest.fn().mockResolvedValue({
                ContentLength: 1000000
            })
        }),
        getObject: jest.fn().mockReturnValue({
            createReadStream: jest.fn().mockReturnValue({
                pipe: jest.fn(),
                on: jest.fn()
            })
        })
    }))
}));

// Mock PlaylistService
jest.mock('../../src/services/playlistService', () => {
    return jest.fn().mockImplementation(() => ({
        getOrCreatePlaylist: jest.fn().mockResolvedValue([
            {
                id: '1',
                type: 'music',
                bucket: 'test-bucket',
                file_key: 'test.mp3'
            }
        ])
    }));
});

describe('StreamingService', () => {
    let streamingService;
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            params: { channelId: '123' },
            headers: { range: 'bytes=0-1000' },
            user: { email: '<EMAIL>' },
            query: { trackIndex: '0' }
        };

        mockRes = {
            status: jest.fn().mockReturnThis(),
            send: jest.fn(),
            writeHead: jest.fn(),
            end: jest.fn(),
            headersSent: false
        };

        streamingService = new StreamingService(mockReq, mockRes);
    });

    describe('startStream', () => {
        it('should return 416 if range header is missing', async () => {
            mockReq.headers.range = undefined;
            await streamingService.startStream();
            expect(mockRes.status).toHaveBeenCalledWith(416);
            expect(mockRes.send).toHaveBeenCalledWith('Requires Range header');
        });

        it('should stream music file successfully', async () => {
            await streamingService.startStream();
            expect(mockRes.writeHead).toHaveBeenCalledWith(206, expect.objectContaining({
                'Content-Type': 'audio/mp3',
                'Accept-Ranges': 'bytes'
            }));
        });
    });

    describe('streamFile', () => {
        it('should return 416 if range is beyond file size', async () => {
            mockReq.headers.range = 'bytes=2000000-3000000';
            await streamingService.streamFile('bucket', 'key.mp3', 1, 0);
            expect(mockRes.status).toHaveBeenCalledWith(416);
        });
    });
});
