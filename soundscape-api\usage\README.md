# Usage API

The Usage API tracks and manages user activity and analytics across the Southern Soundscape platform.

## API Endpoints

### Track Usage

#### POST /api/usage/track
Record user activity.

Request body:
```json
{
  "userId": "string",
  "eventType": "play|pause|skip|like|share",
  "contentId": "string",
  "contentType": "track|album|playlist",
  "timestamp": "string",
  "metadata": {
    "duration": number,
    "position": number,
    "quality": "string"
  }
}
```

### Analytics

#### GET /api/usage/stats/user/:userId
Get user activity statistics.

Response:
```json
{
  "totalListeningTime": number,
  "favoriteTracks": ["string"],
  "favoriteGenres": ["string"],
  "activeHours": {
    "hour": number,
    "count": number
  }[]
}
```

#### GET /api/usage/stats/track/:trackId
Get track usage statistics.

#### GET /api/usage/stats/artist/:artistId
Get artist usage statistics.

### Reports

#### GET /api/usage/reports/daily
Get daily usage report.

Query parameters:
- `date`: Report date (YYYY-MM-DD)
- `type`: Report type (plays|users|genres)

#### GET /api/usage/reports/monthly
Get monthly usage report.

## Error Responses

All endpoints return standard error responses:

```json
{
  "error_code": "string",
  "error_description": "string"
}
```

Common error codes:
- 400: Invalid Parameters
- 403: Unauthorized Access
- 404: Resource Not Found
- 500: Server Error
