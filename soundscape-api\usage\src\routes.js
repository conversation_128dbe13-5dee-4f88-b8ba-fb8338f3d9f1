
const usage = require('./usage');
const { authenticate } = require('../../shared/middleware/authMiddleware');
const errorHandler = require('../../shared/middleware/errorHandler');

module.exports = function (app) {
    // Health check
    app.route('/health')
        .get((req, res) => res.sendStatus(200));

    // Apply authentication middleware to all usage routes
    app.use('/api/usage', authenticate);

    // Session management
    app.route('/api/usage/startsession')
        .post(usage.PostStartSession);

    // Track playback events
    app.route('/api/usage/play')
        .post(usage.postPlay);

    app.route('/api/usage/pause')
        .post(usage.postPause);

    app.route('/api/usage/resume')
        .post(usage.postResume);

    app.route('/api/usage/skip')
        .post(usage.postSkip);

    app.route('/api/usage/complete')
        .post(usage.postComplete);

    // Usage statistics
    app.route('/api/usage/usage')
        .get(usage.getUsage);

    app.route('/api/usage/usagebyuser')
        .get(usage.getUsageByUser);

    // Use shared error handler
    app.use(errorHandler);
}
