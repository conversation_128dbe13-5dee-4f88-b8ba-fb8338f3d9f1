const db = require('../database');

class UsageService {
    constructor(req) {
        this.req = req;
        this.usageParam = req.query.param ?? '';
        this.userId = this.getUserId();
    }

    // Helper method to get user ID from either auth or anonymous session
    getUserId() {
        // If user is authenticated, use their ID from auth middleware
        if (this.req.user && this.req.user.id) {
            return this.req.user.id;
        }

        // For anonymous users, use session ID or generate one
        return this.req.session?.anonymousId || `anon_${Date.now()}`;
    }

    // Start a new play session when user launches a channel
    async startPlaySession() {
        const { channel_name, device } = this.req.body;

        const completeUsageSession = {
            channel_name,
            user_id: this.userId,
            device,
            created_date: new Date(),
            is_authenticated: !!this.req.user,
            user_email: this.req.user?.email || null,
            session_id: this.req.session?.id || null
        };

        try {
            const newSession = await db.models.usage_session.create(completeUsageSession);

            // If user is authenticated, associate session with user profile
            if (this.req.user) {
                await this.updateUserProfile(this.req.user.id, {
                    last_session_date: new Date()
                });
            }

            return newSession;
        } catch (error) {
            console.error('Error creating play session:', error);
            throw error;
        }
    }

    // Track individual play events with user context
    async trackPlay() {
        const { played_id, duration, source, device, song_order, played_at, track_id, usage_session_id } = this.req.body;

        const completeUsageEntry = {
            played_id,
            duration,
            source,
            type: 'play',
            device,
            song_order,
            played_at,
            created_date: new Date(),
            user_id: this.userId,
            track_id,
            usage_session_id,
            is_authenticated: !!this.req.user,
            user_email: this.req.user?.email || null
        };

        try {
            const newEntry = await db.models.usage_entry.create(completeUsageEntry);

            // Update track play count
            if (track_id) {
                await db.models.track.increment('play_count', {
                    where: { id: track_id }
                });
            }

            return newEntry;
        } catch (error) {
            console.error('Error tracking play:', error);
            throw error;
        }
    }

    // Track pause events
    async trackPause() {
        const { track_id, usage_session_id, played_at } = this.req.body;

        return this.createUsageEntry({
            ...this.req.body,
            type: 'pause',
            played_at,
            created_date: new Date()
        });
    }

    // Track resume events
    async trackResume() {
        const { track_id, usage_session_id, played_at } = this.req.body;

        return this.createUsageEntry({
            ...this.req.body,
            type: 'resume',
            played_at,
            created_date: new Date()
        });
    }

    // Track skip events
    async trackSkip() {
        const { track_id, usage_session_id, played_at } = this.req.body;

        return this.createUsageEntry({
            ...this.req.body,
            type: 'skip',
            played_at,
            created_date: new Date()
        });
    }

    // Track complete events (full play)
    async trackComplete() {
        const { track_id, usage_session_id, played_at, duration } = this.req.body;

        return this.createUsageEntry({
            ...this.req.body,
            type: 'complete',
            played_at,
            created_date: new Date()
        });
    }

    // Helper method to create usage entries
    async createUsageEntry(data) {
        try {
            const newEntry = await db.models.usage_entry.create(data);
            return newEntry;
        } catch (error) {
            console.error('Error creating usage entry:', error);
            throw error;
        }
    }

    // Get usage sessions by user with authentication status
    async getUsageSessionByUser() {
        if (!this.userId) {
            throw new Error('user_id is required');
        }

        const whereClause = this.req.user ?
            { user_id: this.userId } :
            {
                user_id: this.userId,
                is_authenticated: false
            };

        try {
            const sessions = await db.models.usage_session.findAll({
                where: whereClause,
                attributes: [
                    'id',
                    'channel_name',
                    'device',
                    'created_date',
                    'is_authenticated',
                    'session_id'
                ],
                include: [
                    {
                        model: db.models.user,
                        as: 'user',
                        attributes: ['id', 'username', 'name'],
                        required: false
                    },
                    {
                        model: db.models.usage_entry,
                        as: 'usageEntries',
                        attributes: [
                            'type',
                            'duration',
                            'played_at',
                            'created_date',
                            'track_id'
                        ],
                        include: [
                            {
                                model: db.models.track,
                                as: 'track',
                                attributes: ['id', 'title', 'album_id']
                            }
                        ]
                    }
                ],
                order: [['created_date', 'DESC']]
            });
            return sessions;
        } catch (error) {
            console.error('Error fetching usage sessions by user:', error);
            throw error;
        }
    }

    // Get all usage sessions
    async getUsageSessions() {
        try {
            const sessions = await db.models.usage_session.findAll({
                include: [
                    {
                        model: db.models.user,
                        as: 'user',
                        attributes: ['id', 'username', 'name']
                    },
                    {
                        model: db.models.usage_entry,
                        as: 'usageEntries',
                        attributes: ['type', 'duration', 'played_at', 'created_date']
                    }
                ]
            });
            return sessions;
        } catch (error) {
            console.error('Error fetching usage sessions:', error);
            throw error;
        }
    }

    // Helper method to update user profile
    async updateUserProfile(userId, updates) {
        try {
            await db.models.user.update(updates, {
                where: { id: userId }
            });
        } catch (error) {
            console.error('Error updating user profile:', error);
            // Don't throw - this is a non-critical operation
        }
    }

    // Convert anonymous user sessions to authenticated user
    async convertAnonymousSession(anonymousId, authenticatedUserId) {
        try {
            await db.models.usage_session.update(
                {
                    user_id: authenticatedUserId,
                    is_authenticated: true
                },
                {
                    where: {
                        user_id: anonymousId,
                        is_authenticated: false
                    }
                }
            );

            await db.models.usage_entry.update(
                {
                    user_id: authenticatedUserId,
                    is_authenticated: true
                },
                {
                    where: {
                        user_id: anonymousId,
                        is_authenticated: false
                    }
                }
            );
        } catch (error) {
            console.error('Error converting anonymous session:', error);
            throw error;
        }
    }
}

module.exports = UsageService;
