const UsageService = require('./services/usageService');

module.exports = {
    getUsage: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.getUsageSessions();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    getUsageByUser: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.getUsageSessionByUser();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    PostStartSession: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.startPlaySession();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    postPlay: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.trackPlay();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    postPause: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.trackPause();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    postResume: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.trackResume();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    postSkip: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.trackSkip();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    },

    postComplete: async function (req, res, next) {
        try {
            const usageService = new UsageService(req);
            let result = await usageService.trackComplete();
            res.json(result);
        } catch (e) {
            return next(e);
        }
    }
};
