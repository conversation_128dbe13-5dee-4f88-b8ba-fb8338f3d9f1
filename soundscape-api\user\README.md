# User API

The User API provides endpoints for managing user profiles, account settings, and preferences in the Southern Soundscape platform.

## Setup

1. Install dependencies:
```bash
cd soundscape-api/user
npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
```

Required environment variables:
```bash
NODE_ENV=development
PORT=3000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=soundscape
DB_USER=ss
DB_PASSWORD=password
JWT_SECRET=your-secret-key
```

3. Start the service:
```bash
npm start        # Production mode
npm run dev      # Development mode
```

## Testing

```bash
npm test                # Run all tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Generate coverage report
```

## Authentication

All endpoints require authentication using OAuth. Include the Bearer token in the Authorization header:
```
Authorization: Bearer <token>
```

## API Endpoints

### Profile Management

#### GET /api/user/profile
Returns the current user's profile information.

Response:
```json
{
    "id": "string",
    "email": "string",
    "name": "string",
    "gender": "string",
    "phone": "string",
    "description": "string",
    "image": "string",
    "is_public": boolean,
    "url": "string",
    "created_at": "string",
    "updated_at": "string"
}
```

#### PUT /api/user/profile
Update user profile information.

Request body:
```json
{
    "name": "string",
    "gender": "string",
    "phone": "string",
    "description": "string",
    "image": "string",
    "is_public": boolean,
    "url": "string"
}
```

### Account Management

#### PUT /api/user/email
Update user's email address.

Request body:
```json
{
    "email": "string",
    "password": "string"
}
```

#### PUT /api/user/password
Update user's password.

Request body:
```json
{
    "current_password": "string",
    "new_password": "string"
}
```

### Preferences

#### GET /api/user/preferences
Get user preferences.

Response:
```json
{
    "language": "string",
    "theme": "light|dark",
    "notifications": {
        "email": boolean,
        "push": boolean
    },
    "privacy": {
        "profile_visible": boolean,
        "activity_visible": boolean
    }
}
```

#### PUT /api/user/preferences
Update user preferences.

Request body:
```json
{
    "language": "string",
    "theme": "light|dark",
    "notifications": {
        "email": boolean,
        "push": boolean
    },
    "privacy": {
        "profile_visible": boolean,
        "activity_visible": boolean
    }
}
```

## Error Responses

All endpoints return standard error responses:

```json
{
    "error_code": "string",
    "error_description": "string"
}
```

Common error codes:
- `unauthorized`: Invalid or missing token
- `invalid_credentials`: Invalid password
- `validation_error`: Invalid request parameters
- `not_found`: Resource not found
- `server_error`: Internal server error

## Project Structure

```
user/
├── app.js          # Application entry point
├── routes/         # API routes
├── controllers/    # Request handlers
├── services/       # Business logic
├── models/         # Data models
└── tests/          # Unit tests
```

## Contributing

1. Create a feature branch
2. Make changes and add tests
3. Run tests and ensure they pass
4. Submit pull request

## License

ISC
