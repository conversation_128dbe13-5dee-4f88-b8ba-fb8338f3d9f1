{"name": "ss-user-api", "version": "1.0.0", "description": "Southern Soundscape User API", "main": "app.js", "scripts": {"start": ". /etc/environment; node app.js", "dev": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"cookie-parser": "^1.4.5", "cors": "^2.8.5", "express": "^4.17.1", "fs": "^0.0.1-security", "http-errors": "^2.0.0", "node-cache": "^5.1.2", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3"}}