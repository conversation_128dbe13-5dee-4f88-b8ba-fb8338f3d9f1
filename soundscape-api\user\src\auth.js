const db = require('./database');
const createError = require('http-errors');

module.exports = {
    // Simple login endpoint for development
    login: async (req, res, next) => {
        try {
            const { email, password } = req.body;
            
            if (!email || !password) {
                return res.status(400).json({
                    error_code: 'missing_credentials',
                    error_description: 'Email and password are required'
                });
            }

            // Find user by email
            const user = await db.models.user.findOne({
                where: { email: email }
            });

            if (!user) {
                return res.status(401).json({
                    error_code: 'invalid_credentials',
                    error_description: 'Invalid email or password'
                });
            }

            // Simple password check (in production, use proper hashing)
            if (user.password !== password) {
                return res.status(401).json({
                    error_code: 'invalid_credentials',
                    error_description: 'Invalid email or password'
                });
            }

            // Update last login
            await user.update({
                last_login: new Date(),
                updated_date: new Date()
            });

            // Return user data (excluding password)
            const userData = {
                id: user.id,
                email: user.email,
                name: user.name,
                gender: user.gender,
                phone: user.phone,
                description: user.description,
                image: user.image,
                is_public: user.is_public,
                url: user.url,
                is_active: user.is_active,
                is_admin: user.is_admin,
                created_date: user.created_date,
                updated_date: user.updated_date,
                last_login: user.last_login
            };

            res.json({
                user: userData,
                token: `mock-token-${email}` // Simple mock token
            });
        } catch (e) {
            next(e);
        }
    },

    // Simple register endpoint for development
    register: async (req, res, next) => {
        try {
            const { name, email, password } = req.body;
            
            if (!name || !email || !password) {
                return res.status(400).json({
                    error_code: 'missing_fields',
                    error_description: 'Name, email, and password are required'
                });
            }

            // Check if user already exists
            const existingUser = await db.models.user.findOne({
                where: { email: email }
            });

            if (existingUser) {
                return res.status(409).json({
                    error_code: 'user_exists',
                    error_description: 'User with this email already exists'
                });
            }

            // Create new user
            const newUser = await db.models.user.create({
                name: name,
                email: email,
                password: password, // In production, hash this
                is_active: true,
                is_admin: false,
                created_date: new Date(),
                updated_date: new Date(),
                last_login: new Date()
            });

            // Return user data (excluding password)
            const userData = {
                id: newUser.id,
                email: newUser.email,
                name: newUser.name,
                gender: newUser.gender,
                phone: newUser.phone,
                description: newUser.description,
                image: newUser.image,
                is_public: newUser.is_public,
                url: newUser.url,
                is_active: newUser.is_active,
                is_admin: newUser.is_admin,
                created_date: newUser.created_date,
                updated_date: newUser.updated_date,
                last_login: newUser.last_login
            };

            res.status(201).json({
                user: userData,
                token: `mock-token-${email}` // Simple mock token
            });
        } catch (e) {
            next(e);
        }
    }
};
