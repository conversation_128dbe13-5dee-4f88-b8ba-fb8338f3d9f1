const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Database connection
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'soundscape',
  username: process.env.DB_USER || 'ss',
  password: process.env.DB_PASSWORD || 'password',
  logging: false
});

// Load models
const modelsPath = path.join(__dirname, '../../shared/models');
const models = {};

// Check if shared models directory exists, otherwise use database models
const modelsDirPath = fs.existsSync(modelsPath) 
  ? modelsPath 
  : path.join(__dirname, '../../../soundscape-database/models');

// Load all model files
fs.readdirSync(modelsDirPath)
  .filter(file => file.endsWith('.js') && file !== 'index.js')
  .forEach(file => {
    const model = require(path.join(modelsDirPath, file))(sequelize);
    models[model.name] = model;
  });

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

module.exports = {
  sequelize,
  models
};
