const user = require('./user');
const { authenticate } = require('../../shared/middleware/authMiddleware');
const isHttpError = require('http-errors').isHttpError;

module.exports = function (app) {
    // Apply authentication middleware to all user routes
    app.use('/api/user', authenticate);

    // Profile management
    app.route('/api/user/profile')
        .get(user.getUserProfile)
        .put(user.updateUserProfile);

    // Account management
    app.route('/api/user/email')
        .put(user.updateEmail);

    app.route('/api/user/password')
        .put(user.updatePassword);

    app.route('/api/user/account')
        .delete(user.deleteAccount);

    // Preferences
    app.route('/api/user/preferences')
        .get(user.getPreferences)
        .put(user.updatePreferences);

    // Subscription Management
    app.route('/api/user/subscription')
        .get(user.getSubscription)
        .post(user.subscribe)
        .delete(user.cancelSubscription);

    // Error handling
    app.use(function (err, req, res, next) {
        if (err) {
            if (isHttpError(err)) {
                return res.status(err.status).json({
                    error_code: err.name.toLowerCase(),
                    error_description: err.message
                });
            }
        }

        return res.status(500).json({
            error_code: 'server_error',
            error_description: 'A server error occurred'
        });
    });
};
