const db = require('../database');
const createError = require('http-errors');

class UserService {
    constructor(req) {
        this.req = req;
        this.userId = req.user.id;
    }

    async getUserProfile() {
        const user = await db.models.user.findByPk(this.userId, {
            attributes: { exclude: ['password'] }
        });

        if (!user) {
            throw createError(404, 'User not found');
        }

        return user;
    }

    async updateUserProfile(updates) {
        const allowedFields = [
            'name',
            'gender',
            'phone',
            'description',
            'image',
            'is_public',
            'url'
        ];

        // Filter out non-allowed fields
        const filteredUpdates = Object.keys(updates)
            .filter(key => allowedFields.includes(key))
            .reduce((obj, key) => {
                obj[key] = updates[key];
                return obj;
            }, {});

        if (Object.keys(filteredUpdates).length === 0) {
            throw createError(400, 'No valid fields to update');
        }

        const [updateCount] = await db.models.user.update(
            {
                ...filteredUpdates,
                updated_date: new Date()
            },
            {
                where: { id: this.userId }
            }
        );

        if (updateCount === 0) {
            throw createError(404, 'User not found');
        }

        return this.getUserProfile();
    }

    async updateEmail(newEmail, password) {
        // Verify password first
        const user = await db.models.user.findByPk(this.userId);
        if (!user) {
            throw createError(404, 'User not found');
        }

        // In real implementation, would verify password hash
        if (user.password !== password) {
            throw createError(401, 'Invalid password');
        }

        // Check if email is already in use
        const existingUser = await db.models.user.findOne({
            where: { email: newEmail }
        });

        if (existingUser) {
            throw createError(409, 'Email already in use');
        }

        await user.update({
            email: newEmail,
            updated_date: new Date()
        });

        return this.getUserProfile();
    }

    async updatePassword(currentPassword, newPassword) {
        const user = await db.models.user.findByPk(this.userId);
        if (!user) {
            throw createError(404, 'User not found');
        }

        // In real implementation, would verify password hash
        if (user.password !== currentPassword) {
            throw createError(401, 'Invalid current password');
        }

        // In real implementation, would hash the new password
        await user.update({
            password: newPassword,
            updated_date: new Date()
        });

        return { success: true };
    }

    async deleteAccount(password) {
        const user = await db.models.user.findByPk(this.userId);
        if (!user) {
            throw createError(404, 'User not found');
        }

        // In real implementation, would verify password hash
        if (user.password !== password) {
            throw createError(401, 'Invalid password');
        }

        await user.update({
            is_active: false,
            updated_date: new Date()
        });

        return { success: true };
    }

    async getPreferences() {
        const preferences = await db.models.user_preference.findAll({
            where: { user_id: this.userId }
        });

        return preferences;
    }

    async updatePreferences(preferences) {
        await db.models.user_preference.bulkCreate(
            preferences.map(pref => ({
                user_id: this.userId,
                key: pref.key,
                value: pref.value
            })),
            {
                updateOnDuplicate: ['value']
            }
        );

        return this.getPreferences();
    }

    async getSubscription() {
        const subscription = await db.models.subscription.findOne({
            where: {
                user_id: this.userId,
                status: 'active'
            },
            order: [['created_date', 'DESC']]
        });

        return subscription || { status: 'none' };
    }

    async subscribe(planType) {
        // Check for existing active subscription
        const existingSubscription = await db.models.subscription.findOne({
            where: {
                user_id: this.userId,
                status: 'active'
            }
        });

        if (existingSubscription) {
            throw createError(409, 'Active subscription already exists');
        }

        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + (planType === 'yearly' ? 12 : 1));

        return await db.models.subscription.create({
            user_id: this.userId,
            plan_type: planType,
            status: 'active',
            start_date: new Date(),
            end_date: endDate,
            created_date: new Date(),
            last_payment_date: new Date()
        });
    }

    async cancelSubscription() {
        const subscription = await db.models.subscription.findOne({
            where: {
                user_id: this.userId,
                status: 'active'
            }
        });

        if (!subscription) {
            throw createError(404, 'No active subscription found');
        }

        await subscription.update({
            status: 'cancelled',
            cancelled_date: new Date(),
            updated_date: new Date()
        });

        return { success: true };
    }
}

module.exports = UserService;
