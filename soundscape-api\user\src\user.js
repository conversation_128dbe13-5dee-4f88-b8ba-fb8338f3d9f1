const UserService = require('./services/userService');

module.exports = {
    getUserProfile: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.getUserProfile();
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updateUserProfile: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updateUserProfile(req.body);
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updateEmail: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updateEmail(
                req.body.email,
                req.body.password
            );
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updatePassword: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updatePassword(
                req.body.currentPassword,
                req.body.newPassword
            );
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    deleteAccount: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.deleteAccount(req.body.password);
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    getPreferences: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.getPreferences();
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updatePreferences: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updatePreferences(req.body.preferences);
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    // Subscription management (placeholder implementations)
    getSubscription: async (req, res, next) => {
        try {
            // Placeholder implementation
            res.json({ subscription: null, message: 'Subscription management not implemented yet' });
        } catch (e) {
            next(e);
        }
    },

    subscribe: async (req, res, next) => {
        try {
            // Placeholder implementation
            res.json({ success: false, message: 'Subscription management not implemented yet' });
        } catch (e) {
            next(e);
        }
    },

    cancelSubscription: async (req, res, next) => {
        try {
            // Placeholder implementation
            res.json({ success: false, message: 'Subscription management not implemented yet' });
        } catch (e) {
            next(e);
        }
    }
};