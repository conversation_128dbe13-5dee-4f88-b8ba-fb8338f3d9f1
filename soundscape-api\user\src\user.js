const UserService = require('./services/userService');

module.exports = {
    getUserProfile: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.getUserProfile();
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updateUserProfile: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updateUserProfile(req.body);
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updateEmail: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updateEmail(
                req.body.email,
                req.body.password
            );
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updatePassword: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updatePassword(
                req.body.currentPassword,
                req.body.newPassword
            );
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    deleteAccount: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.deleteAccount(req.body.password);
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    getPreferences: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.getPreferences();
            res.json(result);
        } catch (e) {
            next(e);
        }
    },

    updatePreferences: async (req, res, next) => {
        try {
            const userService = new UserService(req);
            const result = await userService.updatePreferences(req.body.preferences);
            res.json(result);
        } catch (e) {
            next(e);
        }
    }
};