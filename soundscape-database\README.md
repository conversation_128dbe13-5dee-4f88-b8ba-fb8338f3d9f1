# Soundscape Database Setup Guide

This guide will help you set up the Soundscape database on your local machine.

## Prerequisites

- PostgreSQL installed and running on your machine
- Node.js installed

## Setup Steps

### 1. Environment Configuration

Create a `.env` file in the `soundscape-database` directory with the following content:

```
DATABASE_URL=postgres://myuser:mypassword@localhost:5432/soundscape
```

Replace `myuser` and `mypassword` with your PostgreSQL credentials if needed.

### 2. Database Initialization

Run the following scripts in order:

#### Step 1: Create Database

```
node createDatabase.js
```

This script will:
- Create a new PostgreSQL database named 'soundscape'
- Create a database user (if it doesn't exist)
- Grant necessary privileges to the user
- Set up proper permissions for the database

#### Step 2: Initialize Database Schema

```
node initializeDatabase.js
```

This script will:
- Establish a connection to the database
- Create all necessary tables and relationships
- Set up the database schema with proper constraints
- The tables created include: labels, artists, albums, genres, tracks, languages, users, and more

#### Step 3: Seed Database with Initial Data

```
node seedDatabase.js
```

This script will:
- Populate the database with sample data
- Create test users (including '<EMAIL>' and '<EMAIL>' with their respective passwords)
- Add sample music data including albums, artists, tracks, and genres
- Set up channel configurations and translations

## Troubleshooting

If you encounter any issues:

1. Make sure PostgreSQL is running
2. Verify your database credentials in the `.env` file
3. Check that you have the necessary permissions to create databases and users

## Database Connection Details

After setup, you can connect to the database using:
- Host: localhost
- Port: 5432
- Database: soundscape
- User: As specified in your .env file
- Password: As specified in your .env file
