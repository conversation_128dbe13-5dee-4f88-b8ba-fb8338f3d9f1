/**
 * SeedDatabase.js
 *
 * This script populates the database with initial data for testing and development.
 * It creates genres, channels, artists, albums, tracks, and their relationships.
 */

const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Database connection
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'soundscape',
  username: process.env.DB_USER || 'ss',
  password: process.env.DB_PASSWORD || 'password',
  logging: false
});

// Load models
const modelsPath = path.join(__dirname, 'models');
const models = {};

fs.readdirSync(modelsPath)
  .filter(file => file.indexOf('.') !== 0 && file.slice(-3) === '.js' && file !== 'index.js')
  .forEach(file => {
    const model = require(path.join(modelsPath, file))(sequelize);
    models[model.name] = model;
  });

// Associate models
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Seed data
async function seedDatabase() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Connected to database successfully.');

    // Clear existing data (in reverse order of dependencies)
    console.log('Clearing existing data...');
    await models.usage_entry.destroy({ where: {} });
    await models.usage_session.destroy({ where: {} });
    await models.channel_session.destroy({ where: {} });
    await models.channel_track.destroy({ where: {} });
    await models.track_artist.destroy({ where: {} });
    await models.album_artist.destroy({ where: {} });
    await models.genre_channel.destroy({ where: {} });
    await models.track.destroy({ where: {} });
    await models.album.destroy({ where: {} });
    await models.artist.destroy({ where: {} });
    await models.channel.destroy({ where: {} });
    await models.genre.destroy({ where: {} });
    await models.spot_schedule.destroy({ where: {} });
    await models.label.destroy({ where: {} });
    await models.user.destroy({ where: {} });
    console.log('Existing data cleared.');

    // Create genres
    const genres = await models.genre.bulkCreate([
      {
        name: 'Rock',
        description: 'Rock music',
        image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070',
        url: 'rock',
        path: '/rock',
        order: 1,
        is_public: true,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        name: 'Jazz',
        description: 'Jazz music',
        image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?q=80&w=2072',
        url: 'jazz',
        path: '/jazz',
        order: 2,
        is_public: true,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        name: 'Pop',
        description: 'Pop music',
        image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074',
        url: 'pop',
        path: '/pop',
        order: 3,
        is_public: true,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      }
    ]);

    // Create spot schedule (required for channels)
    const spotSchedule = await models.spot_schedule.create({
      name: 'Default Schedule',
      description: 'Default spot schedule',
      is_active: true,
      created_date: new Date(),
      updated_date: new Date()
    });

    // Create channels
    const channels = await models.channel.bulkCreate([
      {
        name: 'Rock Classics',
        description: 'Classic rock hits from the 60s, 70s, and 80s',
        image: 'https://images.unsplash.com/photo-1471478331149-c72f17e33c73?q=80&w=2069',
        url: 'rock-classics',
        path: '/rock-classics',
        order: 1,
        is_public: true,
        is_active: true,
        spot_id: spotSchedule.id,
        created_date: new Date(),
        update_date: new Date()
      },
      {
        name: 'Jazz Hits',
        description: 'The best jazz music of all time',
        image: 'https://images.unsplash.com/photo-1415201364774-f6f0bb35f28f?q=80&w=2070',
        url: 'jazz-hits',
        path: '/jazz-hits',
        order: 2,
        is_public: true,
        is_active: true,
        spot_id: spotSchedule.id,
        created_date: new Date(),
        update_date: new Date()
      },
      {
        name: 'Pop Favorites',
        description: 'Today\'s pop hits and classics',
        image: 'https://images.unsplash.com/photo-1501612780327-45045538702b?q=80&w=2070',
        url: 'pop-favorites',
        path: '/pop-favorites',
        order: 3,
        is_public: true,
        is_active: true,
        spot_id: spotSchedule.id,
        created_date: new Date(),
        update_date: new Date()
      }
    ]);

    // Create genre-channel associations
    await models.genre_channel.bulkCreate([
      {
        genre_id: genres[0].id, // Rock
        channel_id: channels[0].id, // Rock Classics
        is_public: true,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        genre_id: genres[1].id, // Jazz
        channel_id: channels[1].id, // Jazz Hits
        is_public: true,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        genre_id: genres[2].id, // Pop
        channel_id: channels[2].id, // Pop Favorites
        is_public: true,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      }
    ]);

    // Create label
    const label = await models.label.create({
      name: 'Soundscape Records',
      is_public: true,
      is_active: true,
      created_date: new Date(),
      updated_date: new Date()
    });

    // Create artists
    const artists = await models.artist.bulkCreate([
      {
        name: 'The Rock Band',
        description: 'Classic rock band',
        image: 'https://images.unsplash.com/photo-1598387993281-cecf8b71a8f8?q=80&w=2076',
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        name: 'Jazz Ensemble',
        description: 'Jazz group',
        image: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?q=80&w=2070',
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        name: 'Pop Star',
        description: 'Pop artist',
        image: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?q=80&w=2070',
        created_date: new Date(),
        updated_date: new Date()
      }
    ]);

    // Create albums
    const albums = await models.album.bulkCreate([
      {
        title: 'Rock Anthems',
        description: 'Collection of rock anthems',
        image: 'https://images.unsplash.com/photo-1446057032654-9d8885db76c6?q=80&w=2070',
        release_date: new Date('1980-01-01'),
        year: 1980,
        total_tracks: 5,
        album_type: 'album',
        is_active: true,
        label_id: label.id,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Jazz Standards',
        description: 'Classic jazz standards',
        image: 'https://images.unsplash.com/photo-1511379938547-c1f69419868d?q=80&w=2070',
        release_date: new Date('1990-01-01'),
        year: 1990,
        total_tracks: 5,
        album_type: 'album',
        is_active: true,
        label_id: label.id,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Pop Hits',
        description: 'Popular pop songs',
        image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?q=80&w=2070',
        release_date: new Date('2000-01-01'),
        year: 2000,
        total_tracks: 5,
        album_type: 'album',
        is_active: true,
        label_id: label.id,
        created_date: new Date(),
        updated_date: new Date()
      }
    ]);

    // Create album-artist associations
    await models.album_artist.bulkCreate([
      {
        album_id: albums[0].id,
        artist_id: artists[0].id,
        role: 'Primary',
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        album_id: albums[1].id,
        artist_id: artists[1].id,
        role: 'Primary',
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        album_id: albums[2].id,
        artist_id: artists[2].id,
        role: 'Primary',
        created_date: new Date(),
        updated_date: new Date()
      }
    ]);

    // Create tracks with real URLs and images
    const rockTracks = [
      {
        title: 'Rock and Roll All Nite',
        album_id: albums[0].id,
        track_number: 1,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/612/612095_5674468-lq.mp3',
        image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070',
        duration_seconds: 240,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Sweet Emotion',
        album_id: albums[0].id,
        track_number: 2,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635506_11861866-lq.mp3',
        image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070',
        duration_seconds: 280,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Back in Black',
        album_id: albums[0].id,
        track_number: 3,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635565_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070',
        duration_seconds: 260,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Stairway to Heaven',
        album_id: albums[0].id,
        track_number: 4,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635557_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070',
        duration_seconds: 480,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Bohemian Rhapsody',
        album_id: albums[0].id,
        track_number: 5,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635558_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070',
        duration_seconds: 360,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      }
    ];

    const jazzTracks = [
      {
        title: 'Take Five',
        album_id: albums[1].id,
        track_number: 1,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/631/631346_1648170-lq.mp3',
        image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?q=80&w=2072',
        duration_seconds: 320,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'So What',
        album_id: albums[1].id,
        track_number: 2,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/632/632536_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?q=80&w=2072',
        duration_seconds: 340,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Autumn Leaves',
        album_id: albums[1].id,
        track_number: 3,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/632/632537_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?q=80&w=2072',
        duration_seconds: 300,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Summertime',
        album_id: albums[1].id,
        track_number: 4,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/632/632538_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?q=80&w=2072',
        duration_seconds: 280,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'My Favorite Things',
        album_id: albums[1].id,
        track_number: 5,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/632/632539_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1511192336575-5a79af67a629?q=80&w=2072',
        duration_seconds: 360,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      }
    ];

    const popTracks = [
      {
        title: 'Shape of You',
        album_id: albums[2].id,
        track_number: 1,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635559_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074',
        duration_seconds: 240,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Uptown Funk',
        album_id: albums[2].id,
        track_number: 2,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635560_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074',
        duration_seconds: 270,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Billie Jean',
        album_id: albums[2].id,
        track_number: 3,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635561_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074',
        duration_seconds: 290,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Bad Guy',
        album_id: albums[2].id,
        track_number: 4,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635562_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074',
        duration_seconds: 210,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      },
      {
        title: 'Blinding Lights',
        album_id: albums[2].id,
        track_number: 5,
        disc_number: 1,
        url: 'https://cdn.freesound.org/previews/635/635563_13379318-lq.mp3',
        image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?q=80&w=2074',
        duration_seconds: 230,
        is_active: true,
        created_date: new Date(),
        updated_date: new Date()
      }
    ];

    // Create all tracks
    const allTracks = [...rockTracks, ...jazzTracks, ...popTracks];
    const tracks = await models.track.bulkCreate(allTracks);

    // Create track-artist associations
    const trackArtistAssociations = [];

    // Associate rock tracks with rock artist
    for (let i = 0; i < rockTracks.length; i++) {
      trackArtistAssociations.push({
        track_id: tracks[i].id,
        artist_id: artists[0].id,
        role: 'Primary'
      });
    }

    // Associate jazz tracks with jazz artist
    for (let i = 0; i < jazzTracks.length; i++) {
      trackArtistAssociations.push({
        track_id: tracks[rockTracks.length + i].id,
        artist_id: artists[1].id,
        role: 'Primary'
      });
    }

    // Associate pop tracks with pop artist
    for (let i = 0; i < popTracks.length; i++) {
      trackArtistAssociations.push({
        track_id: tracks[rockTracks.length + jazzTracks.length + i].id,
        artist_id: artists[2].id,
        role: 'Primary'
      });
    }

    // Skip track_artist associations for now
    // await models.track_artist.bulkCreate(trackArtistAssociations);

    // Create channel-track associations
    const channelTrackAssociations = [];

    // Associate rock tracks with rock channel
    for (let i = 0; i < rockTracks.length; i++) {
      channelTrackAssociations.push({
        channel_id: channels[0].id,
        track_id: tracks[i].id,
        weight: 1
      });
    }

    // Associate jazz tracks with jazz channel
    for (let i = 0; i < jazzTracks.length; i++) {
      channelTrackAssociations.push({
        channel_id: channels[1].id,
        track_id: tracks[rockTracks.length + i].id,
        weight: 1
      });
    }

    // Associate pop tracks with pop channel
    for (let i = 0; i < popTracks.length; i++) {
      channelTrackAssociations.push({
        channel_id: channels[2].id,
        track_id: tracks[rockTracks.length + jazzTracks.length + i].id,
        weight: 1
      });
    }

    await models.channel_track.bulkCreate(channelTrackAssociations);

    console.log('Database seeded successfully!');
    console.log(`Created ${genres.length} genres`);
    console.log(`Created ${channels.length} channels`);
    console.log(`Created ${artists.length} artists`);
    console.log(`Created ${albums.length} albums`);
    console.log(`Created ${tracks.length} tracks`);
    console.log(`Created ${channelTrackAssociations.length} channel-track associations`);

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the seed function
seedDatabase();