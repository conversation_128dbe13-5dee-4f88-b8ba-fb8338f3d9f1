/**
 * Add test users to the database
 */

const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Database connection
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'soundscape',
  username: process.env.DB_USER || 'ss',
  password: process.env.DB_PASSWORD || 'password',
  logging: false
});

// Load models
const modelsPath = path.join(__dirname, 'models');
const models = {};

fs.readdirSync(modelsPath)
  .filter(file => file.indexOf('.') !== 0 && file.slice(-3) === '.js' && file !== 'index.js')
  .forEach(file => {
    const model = require(path.join(modelsPath, file))(sequelize);
    models[model.name] = model;
  });

// Associate models
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

async function addTestUsers() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Connected to database successfully.');

    // Clear existing users
    await models.user.destroy({ where: {} });
    console.log('Cleared existing users.');

    // Create test users
    const testUsers = await models.user.bulkCreate([
      {
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Admin User',
        is_active: true,
        is_admin: true,
        created_date: new Date(),
        updated_date: new Date(),
        gender: 'Other',
        phone: '******-0100',
        description: 'System Administrator',
        is_public: false,
        last_login: new Date()
      },
      {
        email: '<EMAIL>',
        password: 'test123',
        name: 'Test User',
        is_active: true,
        is_admin: false,
        created_date: new Date(),
        updated_date: new Date(),
        gender: 'Other',
        phone: '******-0101',
        description: 'Test user for development',
        is_public: true,
        last_login: new Date()
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        name: 'Demo User',
        is_active: true,
        is_admin: false,
        created_date: new Date(),
        updated_date: new Date(),
        gender: 'Other',
        phone: '******-0102',
        description: 'Demo user for testing',
        is_public: true,
        last_login: new Date()
      }
    ]);

    console.log(`Created ${testUsers.length} test users successfully!`);
    testUsers.forEach(user => {
      console.log(`- ${user.name} (${user.email})`);
    });

  } catch (error) {
    console.error('Error adding test users:', error);
  } finally {
    await sequelize.close();
  }
}

addTestUsers();
