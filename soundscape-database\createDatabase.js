const { Client } = require('pg');

const superuserClient = new Client({
  user: 'postgres', // Superuser
  host: 'localhost',
  database: 'postgres', // Default database initially
  password: 'your_superuser_password', // Replace with your superuser password
  port: 5432,
});

const dbName = 'soundscape';
const dbUser = 'ss';
const dbPassword = 'password';

async function createDatabaseAndUser() {
  try {
    await superuserClient.connect();

    // Create the user if it doesn’t exist
    const userResult = await superuserClient.query(`SELECT 1 FROM pg_roles WHERE rolname = '${dbUser}'`);
    if (userResult.rows.length === 0) {
      await superuserClient.query(`CREATE USER ${dbUser} WITH PASSWORD '${dbPassword}'`);
      console.log(`User ${dbUser} created.`);
    } else {
      console.log(`User ${dbUser} already exists.`);
    }

    // Create the database if it doesn’t exist, owned by db<PERSON><PERSON>
    const dbResult = await superuserClient.query(`SELECT 1 FROM pg_database WHERE datname = '${dbName}'`);
    if (dbResult.rows.length === 0) {
      await superuserClient.query(`CREATE DATABASE ${dbName} WITH OWNER ${dbUser}`);
      console.log(`Database ${dbName} created with owner ${dbUser}.`);
    } else {
      console.log(`Database ${dbName} already exists.`);
    }

    // Grant database-level privileges (redundant if owned by ss, but good practice)
    await superuserClient.query(`GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO ${dbUser}`);
    console.log(`Granted all privileges on database ${dbName} to user ${dbUser}.`);

    // Switch to the soundscape database to apply schema-level grants
    await superuserClient.end(); // Close initial connection
    const soundscapeClient = new Client({
      user: 'postgres',
      host: 'localhost',
      database: dbName, // Connect to soundscape db
      password: 'your_superuser_password',
      port: 5432,
    });
    await soundscapeClient.connect();

    // Grant specific privileges on the public schema
    await soundscapeClient.query(`GRANT USAGE ON SCHEMA public TO ${dbUser}`);
    await soundscapeClient.query(`GRANT CREATE ON SCHEMA public TO ${dbUser}`);
    await soundscapeClient.query(`GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${dbUser}`);
    await soundscapeClient.query(`GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${dbUser}`);
    await soundscapeClient.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${dbUser}`);
    await soundscapeClient.query(`ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${dbUser}`);
    console.log(`Granted necessary privileges on schema public to user ${dbUser}.`);

    await soundscapeClient.end();
  } catch (err) {
    console.error('Error setting up database and user:', err);
  } finally {
    await superuserClient.end();
  }
}

createDatabaseAndUser();