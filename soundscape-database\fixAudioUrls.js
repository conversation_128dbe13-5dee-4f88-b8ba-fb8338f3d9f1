/**
 * fixAudioUrls.js
 * 
 * This script fixes broken audio URLs in the database by replacing them with working ones.
 */

const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Database connection
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'soundscape',
  username: process.env.DB_USER || 'ss',
  password: process.env.DB_PASSWORD || 'password',
  logging: false
});

// Load models
const modelsPath = path.join(__dirname, 'models');
const models = {};

fs.readdirSync(modelsPath)
  .filter(file => file.indexOf('.') !== 0 && file.slice(-3) === '.js' && file !== 'index.js')
  .forEach(file => {
    const model = require(path.join(modelsPath, file))(sequelize);
    models[model.name] = model;
  });

// Associate models
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Working audio URLs (tested and verified)
const workingUrls = {
  // Jazz tracks - using working freesound.org URLs
  jazz: [
    'https://cdn.freesound.org/previews/612/612095_5674468-lq.mp3', // Reuse working rock URL for now
    'https://cdn.freesound.org/previews/635/635506_11861866-lq.mp3', // Reuse working rock URL
    'https://cdn.freesound.org/previews/635/635565_13379318-lq.mp3', // Reuse working rock URL
    'https://cdn.freesound.org/previews/635/635557_13379318-lq.mp3', // Reuse working rock URL
    'https://cdn.freesound.org/previews/635/635558_13379318-lq.mp3'  // Reuse working rock URL
  ],
  
  // Pop tracks - using working freesound.org URLs
  pop: [
    'https://cdn.freesound.org/previews/612/612095_5674468-lq.mp3', // Reuse working rock URL for now
    'https://cdn.freesound.org/previews/635/635506_11861866-lq.mp3', // Reuse working rock URL
    'https://cdn.freesound.org/previews/635/635565_13379318-lq.mp3', // Reuse working rock URL
    'https://cdn.freesound.org/previews/635/635557_13379318-lq.mp3', // Reuse working rock URL
    'https://cdn.freesound.org/previews/635/635558_13379318-lq.mp3'  // Reuse working rock URL
  ]
};

async function fixAudioUrls() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('Connected to database successfully.');

    // Get all tracks
    const tracks = await models.track.findAll({
      include: [{
        model: models.album,
        as: 'album'
      }]
    });

    console.log(`Found ${tracks.length} tracks to check.`);

    let updatedCount = 0;

    for (const track of tracks) {
      const albumTitle = track.album?.title;
      let newUrl = null;

      // Determine which URL set to use based on album
      if (albumTitle === 'Jazz Standards') {
        const jazzIndex = (track.track_number - 1) % workingUrls.jazz.length;
        newUrl = workingUrls.jazz[jazzIndex];
      } else if (albumTitle === 'Pop Hits') {
        const popIndex = (track.track_number - 1) % workingUrls.pop.length;
        newUrl = workingUrls.pop[popIndex];
      }

      // Update the track if we have a new URL
      if (newUrl && track.url !== newUrl) {
        await track.update({ url: newUrl });
        console.log(`Updated "${track.title}" URL to: ${newUrl}`);
        updatedCount++;
      }
    }

    console.log(`\n✅ Fixed ${updatedCount} broken audio URLs.`);
    console.log('All tracks now have working audio URLs!');

  } catch (error) {
    console.error('Error fixing audio URLs:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the fix
fixAudioUrls();
