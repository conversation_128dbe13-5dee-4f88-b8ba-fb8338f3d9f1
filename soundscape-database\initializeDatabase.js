const { connection, models } = require('./dbSetup');

async function initializeDatabase() {
    try {
        await connection.authenticate();
        console.log('Connection to the database has been established successfully.');

        // Log the username used for the connection
        console.log(`Connected as user: ${connection.config.username}`);

        // Log models for debugging
        console.log(`Models: ${Object.keys(models).join(', ')}`);

        // Check and log if the sync method exists for specific models
        ['label', 'channel_track', 'genre_channel'].forEach(modelName => {
            if (models[modelName] && typeof models[modelName].sync === 'function') {
                console.log(`${modelName} exists and has a sync method.`);
            } else {
                console.log(`${modelName} does not exist or does not have a sync method.`);
            }
        });

        // Synchronize models in order
        const modelNames = [
            'label', 'artist', 'album', 'genre', 'track', 'language','translation',
            'spot_schedule', 'channel', 'channel_session', 'user',
            'usage_session', 'usage_entry', 'track_artist', 'album_artist', 'channel_track', 'genre_channel'
        ];
        // Check and log if the sync method exists for all models
        for (const modelName of modelNames) {
            if (models[modelName] && typeof models[modelName].sync === 'function') {
                console.log(`Synchronizing model: ${modelName}`);
                await models[modelName].sync({ force: true });
            } else {
                console.log(`Model ${modelName} does not exist or does not have a sync method.`);
            }
        }

        console.log('All models were synchronized successfully.');
    } catch (error) {
        console.error('Unable to connect to the database:', error);
    }
}

initializeDatabase();
