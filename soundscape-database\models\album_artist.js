'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const AlbumArtist = sequelize.define('album_artist', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    album_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'albums',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    artist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'artists',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    role: {
      type: DataTypes.STRING,  // Primary, Featured, Collaborator etc...
      defaultValue: 'Primary'
    },
    created_date: DataTypes.DATE,
    updated_date: DataTypes.DATE
  }, {
    tableName: 'album_artists',
    timestamps: false
  });

  // Define associations
  AlbumArtist.associate = (models) => {
    AlbumArtist.belongsTo(models.album, { foreignKey: 'album_id', as: 'album' });
    AlbumArtist.belongsTo(models.artist, { foreignKey: 'artist_id', as: 'artist' });
  };

  return AlbumArtist;
};