'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('channel_session',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      url: DataTypes.STRING,
      name: DataTypes.STRING,
      image: DataTypes.STRING,
      description: DataTypes.STRING,
      hover_text: DataTypes.STRING,
      is_active: DataTypes.BOOLEAN,
      created_date: DataTypes.DATE,
      updated_date: DataTypes.DATE,

      // Foreign Key
      genre_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'genres',
          key: 'id'
        }
      }
    }, {
    tableName: 'channel_sessions',
    timestamps: false
  });
};