'use strict';

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ChannelTrack = sequelize.define('channel_track', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    channel_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'channels',
        key: 'id'
      }
    },
    track_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'tracks',
        key: 'id'
      }
    },
    weight: DataTypes.INTEGER,
  }, {
    tableName: 'channel_tracks',
    timestamps: false
  });

  // Define associations
  ChannelTrack.associate = (models) => {
    ChannelTrack.belongsTo(models.channel, { foreignKey: 'channel_id', as: 'channel' });
    ChannelTrack.belongsTo(models.track, { foreignKey: 'track_id', as: 'track' });
  };

  return ChannelTrack;
};
