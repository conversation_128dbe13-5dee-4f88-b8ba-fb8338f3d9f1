'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Channel = sequelize.define('channel', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      url: DataTypes.STRING,
      name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      description: DataTypes.STRING,
      image: DataTypes.STRING,
      path: DataTypes.STRING,
      song_total: DataTypes.STRING,
      order: DataTypes.INTEGER,
      is_ad_active: DataTypes.BOOLEAN,
      is_public: DataTypes.BOOLEAN,
      is_active: DataTypes.BOOLEAN,
      created_date: DataTypes.DATE,
      update_date: DataTypes.DATE,

      spot_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'spot_schedules',
          key: 'id'
        }
      },
    }, {
      tableName: 'channels',
      timestamps: false
    });
  
    // Define associations
    Channel.associate = (models) => {
      Channel.hasMany(models.genre_channel, { foreignKey: 'channel_id', as: 'genreChannels' });
      Channel.hasMany(models.channel_track, { foreignKey: 'channel_id', as: 'channelTracks' });  // Association with channel_tracks
    };
    return Channel;
  };