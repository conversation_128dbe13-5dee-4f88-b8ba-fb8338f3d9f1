'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Genre = sequelize.define('genre',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      url: DataTypes.STRING,
      name: DataTypes.STRING,
      description: DataTypes.STRING,
      image: DataTypes.STRING,
      path: DataTypes.STRING,
      order: DataTypes.INTEGER,
      is_public: DataTypes.BOOLEAN,
      is_active: DataTypes.BOOLEAN,
      created_date: DataTypes.DATE,
      updated_date: DataTypes.DATE
    },
    {
      tableName: 'genres',
      timestamps: false
    }
  );

  Genre.associate = (models) => {
    Genre.hasMany(models.genre_channel, { foreignKey: 'genre_id', as: 'genreChannels' });
  };

  return Genre;
};
