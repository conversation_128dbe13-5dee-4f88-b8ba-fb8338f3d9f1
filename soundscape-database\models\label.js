'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('label',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },

      name: DataTypes.STRING,
      is_public: DataTypes.BOOLEAN,
      is_active: DataTypes.BOOLEAN,
      created_date: DataTypes.DATE,
      updated_date: DataTypes.DATE
    }, {
    tableName: 'labels',
    timestamps: false
  });
};