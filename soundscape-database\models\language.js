'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Language = sequelize.define('language', {
    code: {
      type: DataTypes.STRING(10),
      primaryKey: true,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    native_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    is_rtl: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    is_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    }
  }, {
    tableName: 'languages',
    timestamps: false
  });

  return Language;
};