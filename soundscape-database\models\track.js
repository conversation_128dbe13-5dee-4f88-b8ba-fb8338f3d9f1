'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Track = sequelize.define('track', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    album_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'albums',
        key: 'id'
      }
    },
    track_number: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    disc_number: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    url: DataTypes.STRING,
    image: DataTypes.STRING,
    description: DataTypes.STRING,
    duration_seconds: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    is_explicit: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    play_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    hover_text: DataTypes.STRING,
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    created_date: DataTypes.DATE,
    updated_date: DataTypes.DATE,
  }, {
    tableName: 'tracks',
    timestamps: false
  });

  Track.associate = (models) => {
    Track.belongsTo(models.album, { foreignKey: 'album_id', as: 'album' });
    Track.hasMany(models.channel_track, { foreignKey: 'track_id', as: 'channelTracks' });
    Track.hasMany(models.usage_entry, { foreignKey: 'track_id', as: 'usageEntries' });
    Track.hasMany(models.track_artist, { foreignKey: 'track_id', as: 'trackArtists' });
    Track.belongsToMany(models.artist, {
      through: models.track_artist,
      foreignKey: 'track_id',
      as: 'artists'
    });
  };

  return Track;
};
