'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const TrackArtist = sequelize.define('track_artist',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      // Foreign Key
      track_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'tracks',
          key: 'id'
        }
      },

      artist_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'artists',
          key: 'id'
        }
      },
      role: DataTypes.STRING,   // Primary, Featured, Collaborator etc...
    }, {
    tableName: 'track_artists',
    timestamps: false
  });

  TrackArtist.associate = (models) => {
    TrackArtist.belongsTo(models.track, {
      foreignKey: 'track_id',
      as: 'track',
      targetKey: 'id'
    });
    TrackArtist.belongsTo(models.artist, {
      foreignKey: 'artist_id',
      as: 'artist',
      targetKey: 'id'
    });
  };

  return TrackArtist;
};