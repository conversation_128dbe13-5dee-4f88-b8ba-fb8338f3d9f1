'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const Translation = sequelize.define('translation', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    language_code: {
      type: DataTypes.STRING(10),
      allowNull: false,
      references: {
        model: 'languages',
        key: 'code'
      }
    },
    namespace: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    key: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: false
    }
  }, {
    tableName: 'translations',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['language_code', 'namespace', 'key']
      }
    ]
  });

  Translation.associate = (models) => {
    Translation.belongsTo(models.language, { foreignKey: 'language_code', targetKey: 'code' });
  };

  return Translation;
};