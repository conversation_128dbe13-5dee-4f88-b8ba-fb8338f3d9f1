'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const UsageEntry = sequelize.define('usage_entry', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    played_id: DataTypes.INTEGER, // songid, ad_audio, ad_video,
    duration: DataTypes.STRING,  // in seconds
    source: DataTypes.STRING,  // Ad_{advertiser}, or song or sweeper
    type: DataTypes.STRING,  // skip, pause, complete
    device: DataTypes.STRING, // android, ios, web
    song_order: DataTypes.STRING,
    played_at: {
      type: DataTypes.DATE,
      allowNull: false
    },
    created_date: DataTypes.DATE,
    user_id: {
      type: DataTypes.STRING, // Changed to STRING to support both numeric IDs and anonymous IDs
      allowNull: false
    },
    track_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'tracks',
        key: 'id'
      }
    },
    usage_session_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'usage_sessions',
        key: 'id'
      }
    },
    is_authenticated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    user_email: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    tableName: 'usage_entrys',
    timestamps: false
  });

  // Define associations
  UsageEntry.associate = (models) => {
    UsageEntry.belongsTo(models.user, {
      foreignKey: 'user_id',
      as: 'user',
      constraints: false // Allow non-FK values for anonymous users
    });
    UsageEntry.belongsTo(models.track, {
      foreignKey: 'track_id',
      as: 'track'
    });
    UsageEntry.belongsTo(models.usage_session, {
      foreignKey: 'usage_session_id',
      as: 'usageSession'
    });
  };

  return UsageEntry;
};
