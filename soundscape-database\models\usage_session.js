'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  const UsageSession = sequelize.define('usage_session', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    channel_name: DataTypes.STRING,
    user_id: {
      type: DataTypes.STRING, // Changed to STRING to support both numeric IDs and anonymous IDs
      allowNull: false
    },
    device: DataTypes.STRING, // android, ios, web
    created_date: DataTypes.DATE,
    is_authenticated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    user_email: {
      type: DataTypes.STRING,
      allowNull: true
    },
    session_id: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    tableName: 'usage_sessions',
    timestamps: false
  });

  UsageSession.associate = (models) => {
    UsageSession.belongsTo(models.user, {
      foreignKey: 'user_id',
      as: 'user',
      constraints: false // Allow non-FK values for anonymous users
    });
    UsageSession.hasMany(models.usage_entry, {
      foreignKey: 'usage_session_id',
      as: 'usageEntries'
    });
  };

  return UsageSession;
};
