'use strict';

const DataTypes = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define('user',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
      },
      is_admin: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
      },
      created_date: {
        type: DataTypes.DATE,
        allowNull: false
      },
      url: DataTypes.STRING,
      gender: DataTypes.STRING,
      phone: DataTypes.STRING,
      description: DataTypes.STRING,
      image: DataTypes.STRING,
      is_public: DataTypes.BOOLEAN,
      last_login: DataTypes.DATE,
      updated_date: DataTypes.DATE
    }, {
    tableName: 'users',
    timestamps: false
  });
};
