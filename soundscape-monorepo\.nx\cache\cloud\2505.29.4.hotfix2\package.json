{"name": "nx-cloud-client-bundle", "version": "0.0.1", "type": "commonjs", "main": "index.js", "author": "<PERSON>", "license": "proprietary", "dependencies": {"axios": "1.1.3", "enquirer": "2.4.1", "dotenv": "~10.0.0", "node-machine-id": "^1.1.12", "tar": "6.1.11", "strip-json-comments": "^5.0.1", "chalk": "^4.1.0", "yargs-parser": ">=21.1.1", "fs-extra": "^11.1.0", "open": "~8.4.0", "ini": "4.1.3", "yaml": "2.6.1", "semver": "7.5.4"}}