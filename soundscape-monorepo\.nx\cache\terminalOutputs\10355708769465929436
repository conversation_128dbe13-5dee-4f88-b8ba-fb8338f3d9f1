[2m> [22mvite build

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[36mvite v5.4.17 [32mbuilding for production...[36m[39m
transforming...
[32m✓[39m 111 modules transformed.
[31mx[39m Build failed in 558ms
[31merror during build:
[31msrc/components/StreamPlayer.tsx (5:2): "startStreaming" is not exported by "../../soundscape-monorepo-redux/src/index.ts", imported by "src/components/StreamPlayer.tsx".[31m
file: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/components/StreamPlayer.tsx:5:2[31m
[33m
3:   useAppDispatch,
4:   useAppSelector,
5:   startStreaming,
     ^
6:   playTrack,
7:   pauseTrack,
[31m
    at getRollupError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/parseAst.js:397:41)
    at error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/parseAst.js:393:42)
    at Module.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:16603:16)
    at Module.traceVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:17052:29)
    at ModuleScope.findVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:14709:39)
    at ReturnValueScope.findVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:5582:38)
    at FunctionBodyScope.findVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:5582:38)
    at ReturnValueScope.findVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:5582:38)
    at FunctionBodyScope.findVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:5582:38)
    at TrackingScope.findVariable (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/rollup/dist/es/shared/node-entry.js:5582:38)[39m
