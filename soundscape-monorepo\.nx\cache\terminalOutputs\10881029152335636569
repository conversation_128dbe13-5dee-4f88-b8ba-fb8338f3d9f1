[2m> [22mvite build

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[36mvite v5.4.17 [32mbuilding for production...[36m[39m
[96msrc/slices/playerSlice.ts[0m:[93m132[0m:[93m3[0m - [91merror[0m[90m TS2345: [0mArgument of type '({ channelId, trackIndex }: { channelId: string; trackIndex: number; }) => Promise<TrackInfo>' is not assignable to parameter of type 'AsyncThunkPayloadCreator<TrackInfoResponse, { channelId: string; trackIndex: number; }, AsyncThunkConfig>'.
  Type 'Promise<TrackInfo>' is not assignable to type 'AsyncThunkPayloadCreatorReturnValue<TrackInfoResponse, AsyncThunkConfig>'.
    Type 'Promise<TrackInfo>' is not assignable to type 'Promise<RejectWithValue<unknown, unknown>>'.
      Type 'TrackInfo' is missing the following properties from type 'RejectWithValue<unknown, unknown>': payload, meta, _type

[7m132[0m   async ({ channelId, trackIndex }) => {
[7m   [0m [91m  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

transforming...
[32m✓[39m 38 modules transformed.
rendering chunks...

[vite:dts] Start generate declaration files...
computing gzip size...
[2m../dist/soundscape-monorepo-redux/[22m[35mstyle.css  [39m[1m[2m 0.00 kB[22m[1m[22m[2m │ gzip:  0.02 kB[22m
[2m../dist/soundscape-monorepo-redux/[22m[36mindex.mjs  [39m[1m[2m91.01 kB[22m[1m[22m[2m │ gzip: 25.71 kB[22m
[vite:dts] Declaration files built in 1271ms.

[32m✓ built in 1.62s[39m
