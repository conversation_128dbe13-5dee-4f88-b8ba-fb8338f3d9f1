[2m> [22mvite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  [32m[1mVITE[22m v5.4.17[39m  [2mready in [0m[1m395[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4200[22m/[39m
[2m10:23:09 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:23:21 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:23:41 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:23:41 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:23:42 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:23:57 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:23:57 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:23:58 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:24:09 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:24:10 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:24:11 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:24:22 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:24:22 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:24:23 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:24:37 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:24:37 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:24:38 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:24:52 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:24:52 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:24:53 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:25:03 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/slices/playerSlice.ts[22m
[2m10:25:03 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?
[2m10:25:04 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "../config" from "../../soundscape-monorepo-redux/src/services/streamingService.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[39m:1:29
[33m  1  |  import { API_BASE_URL } from "../config";
     |                                ^
  2  |  class StreamingService {
  3  |    constructor() {[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 0)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m10:25:41 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/SelectedGenre.tsx[22m
[2m10:25:54 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/SelectedGenre.tsx[22m
[2m10:26:09 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/SelectedGenre.tsx[22m
[2m10:26:40 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m10:26:52 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m10:27:07 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m10:27:26 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m10:27:39 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m10:27:56 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/index.ts[22m
[2m10:34:48 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/StreamPlayer.tsx[22m
[2m10:35:00 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/StreamPlayer.tsx[22m
[2m10:37:19 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[22m
[2m10:37:35 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[22m
[2m10:37:45 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[22m
[2m10:38:09 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[22m
[2m10:38:23 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/streamingService.ts[22m
[2m10:54:23 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m10:54:44 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m10:57:23 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m10:57:35 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m10:57:52 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m11:00:37 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m11:00:51 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m11:07:48 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
[2m11:08:02 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.tsx[22m
^C