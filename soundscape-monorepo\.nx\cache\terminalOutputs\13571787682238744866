[2m> [22mvite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  [32m[1mVITE[22m v5.4.17[39m  [2mready in [0m[1m411[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4200[22m/[39m
[2m2:35:39 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m2:35:48 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:36:35 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m2:36:47 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m2:36:59 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m2:37:10 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m2:41:46 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m2:41:57 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m2:42:06 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m2:42:30 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m2:42:39 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m2:43:00 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m2:43:12 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:43:25 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:43:40 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:43:53 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m2:44:04 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/app.css[22m
[2m2:44:26 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:46:46 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m2:46:57 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m2:47:06 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m2:47:18 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:47:28 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:49:01 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:51:58 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:52:16 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:52:31 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:32 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:32 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:34 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:34 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:35 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:36 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:36 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:36 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:52:37 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:37 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:52:38 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m2:57:42 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m2:57:52 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m2:57:53 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\apps\soundscape-web\src\App.tsx: Unterminated JSX contents. (105:10)

[0m [90m 103 |[31m       {[90m/* <MusicPlayer /> */[31m}
 [90m 104 |[31m       [33m<[31m[33mAudioPlayer[31m [33m/[31m[33m>[31m
[31m[1m>[22m[31m[90m 105 |[31m     [33m<[31m[33m/[31m[33mdiv[31m[33m>[31m
 [90m     |[31m           [31m[1m^[22m[31m
 [90m 106 |[31m   )[33m;[31m
 [90m 107 |[31m }
 [90m 108 |[31m[0m[39m
  Plugin: [35mvite:react-babel[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/App.tsx[39m:105:10
[33m  103|        {/* <MusicPlayer /> */}
  104|        <AudioPlayer />
  105|      </div>
     |            ^
  106|    );
  107|  }[39m
      at constructor (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:360:19)
      at TypeScriptParserMixin.raise (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:3338:19)
      at TypeScriptParserMixin.jsxReadToken (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6634:20)
      at TypeScriptParserMixin.getTokenFromCode (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6960:12)
      at TypeScriptParserMixin.getTokenFromCode (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9842:11)
      at TypeScriptParserMixin.nextToken (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:2521:10)
      at TypeScriptParserMixin.next (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:2434:10)
      at TypeScriptParserMixin.eat (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:2438:12)
      at TypeScriptParserMixin.expect (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:3667:15)
      at TypeScriptParserMixin.jsxParseClosingElementAt (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6866:10)
      at TypeScriptParserMixin.jsxParseElementAt (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6881:37)
      at TypeScriptParserMixin.jsxParseElementAt (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6884:32)
      at TypeScriptParserMixin.jsxParseElement (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6935:17)
      at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6945:19)
      at TypeScriptParserMixin.parseExprSubscripts (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10785:23)
      at TypeScriptParserMixin.parseUpdate (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10770:21)
      at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10750:23)
      at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9690:18)
      at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10603:61)
      at TypeScriptParserMixin.parseExprOps (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10608:23)
      at TypeScriptParserMixin.parseMaybeConditional (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10585:23)
      at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10538:21)
      at C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9628:39
      at TypeScriptParserMixin.tryParse (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:3676:20)
      at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9628:18)
      at C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10507:39
      at TypeScriptParserMixin.allowInAnd (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12126:12)
      at TypeScriptParserMixin.parseMaybeAssignAllowIn (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10507:17)
      at TypeScriptParserMixin.parseParenAndDistinguishExpression (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:11386:28)
      at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:11033:23)
      at TypeScriptParserMixin.parseExprAtom (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:6950:20)
      at TypeScriptParserMixin.parseExprSubscripts (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10785:23)
      at TypeScriptParserMixin.parseUpdate (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10770:21)
      at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10750:23)
      at TypeScriptParserMixin.parseMaybeUnary (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9690:18)
      at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10603:61)
      at TypeScriptParserMixin.parseExprOps (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10608:23)
      at TypeScriptParserMixin.parseMaybeConditional (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10585:23)
      at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10538:21)
      at TypeScriptParserMixin.parseMaybeAssign (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9639:20)
      at TypeScriptParserMixin.parseExpressionBase (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10491:23)
      at C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10487:39
      at TypeScriptParserMixin.allowInAnd (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12121:16)
      at TypeScriptParserMixin.parseExpression (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:10487:17)
      at TypeScriptParserMixin.parseReturnStatement (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12806:28)
      at TypeScriptParserMixin.parseStatementContent (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12463:21)
      at TypeScriptParserMixin.parseStatementContent (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:9365:18)
      at TypeScriptParserMixin.parseStatementLike (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12432:17)
      at TypeScriptParserMixin.parseStatementListItem (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12412:17)
      at TypeScriptParserMixin.parseBlockOrModuleBlockBody (C:\Users\<USER>\Documents\southernsoundscape\soundscape-monorepo\node_modules\@babel\parser\lib\index.js:12980:61)
[2m2:58:02 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m2:58:13 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m2:58:28 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m2:58:43 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/app.css[22m
[2m2:58:56 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:59:08 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:59:21 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:59:31 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m2:59:42 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m2:59:52 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:00:01 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:00:14 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:00:42 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:00:53 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:01:07 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:01:17 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:01:28 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:01:41 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:01:55 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:02:51 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:51 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:52 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:52 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:53 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:53 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:54 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:54 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:54 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/3/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:58 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:02:59 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:03:01 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/1/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:03:02 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:04:33 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:04:42 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:05:03 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:05:04 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:05:25 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m3:05:29 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:08:00 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:10:48 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m3:11:00 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m3:17:20 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m3:17:33 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:17:44 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:18:12 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Navbar.tsx[22m
[2m3:18:25 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/navbar.css[22m
[2m3:21:20 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.css[22m
[2m3:21:31 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.css[22m
[2m3:21:41 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.css[22m
[2m3:21:53 PM[22m [31m[1m[vite][22m[39m [31mhttp proxy error: /api/channels/2/tracks[39m
AggregateError [ECONNREFUSED]: 
    at internalConnectMultiple (node:net:1116:18)
    at afterConnectMultiple (node:net:1683:7)
[2m3:21:56 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.css[22m
[2m3:22:24 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.css[22m
[2m3:23:35 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/AudioPlayer.css[22m
[2m3:24:20 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/sidebar.css[22m
[2m3:25:41 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/styles/themes.css[22m
[2m3:25:53 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/styles/themes.css[22m
[2m3:30:21 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m3:30:33 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m3:30:49 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m3:31:04 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m3:31:20 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/selectedgenre.css[22m
[2m3:32:58 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Home.tsx[22m
[2m3:33:21 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/home.css[22m
[2m3:34:16 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/components/Sidebar.tsx[22m
[2m3:36:06 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/genrelist.css[22m
[2m3:36:22 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/genrelist.css[22m
[2m3:36:37 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/genrelist.css[22m
[2m3:36:53 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/genrelist.css[22m
[2m3:38:10 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/genrelist.css[22m
[2m3:43:41 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/GenreList.tsx[22m
[2m3:44:47 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/genrelist.css[22m
[2m3:45:23 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m3:45:31 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
^C