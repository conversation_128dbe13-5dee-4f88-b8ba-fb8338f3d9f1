[2m> [22mvite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  [32m[1mVITE[22m v5.4.14[39m  [2mready in [0m[1m339[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4200[22m/[39m
[2m6:16:23 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/index.ts[22m
[2m6:16:29 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/reducers.ts[22m
[2m6:16:32 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/reducers.ts[22m
[2m6:16:35 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/reducers.ts[22m
[2m6:17:01 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Genres.tsx[22m
[2m6:17:14 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Genres.tsx[22m
[2m6:17:46 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/main.tsx[22m
[2m6:18:43 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/main.tsx[22m
[2m6:18:56 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2msrc/main.tsx[22m
[2m6:19:07 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Genres.tsx[22m
[2m6:19:13 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Genres.tsx[22m
[2m6:19:21 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/genreService.ts[22m
[2m6:19:28 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/index.ts[22m
[2m6:19:43 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/thunks/genreThunks.ts[22m
[2m6:20:49 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/services/genreService.ts[22m
[2m6:20:49 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/thunks/genreThunks.ts[22m
[2m6:21:27 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/store.ts[22m
[2m6:21:27 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "redux-logger" from "../../soundscape-monorepo-redux/src/store.ts". Does the file exist?
[2m6:21:27 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "redux-logger" from "../../soundscape-monorepo-redux/src/store.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/store.ts[39m:5:29
[33m  1  |  import { configureStore } from "@reduxjs/toolkit";
  2  |  import { rootReducer } from "./reducers";
  3  |  import { createLogger } from "redux-logger";
     |                                ^
  4  |  const loggerMiddleware = createLogger({
  5  |    collapsed: true,[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49257:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49252:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64199:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64331:39
      at async Promise.all (index 2)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64258:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49098:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:51931:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:62031:24)
[2m6:21:35 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/store.ts[22m
[2m6:21:35 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "redux-logger" from "../../soundscape-monorepo-redux/src/store.ts". Does the file exist?
[2m6:21:35 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "redux-logger" from "../../soundscape-monorepo-redux/src/store.ts". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/store.ts[39m:5:19
[33m  1  |  import { configureStore } from "@reduxjs/toolkit";
  2  |  import { rootReducer } from "./reducers";
  3  |  import logger from "redux-logger";
     |                      ^
  4  |  const loggerMiddleware = logger({
  5  |    collapsed: true,[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49257:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49252:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64199:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64331:39
      at async Promise.all (index 2)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:64258:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:49098:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:51931:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-CHZK6zbr.js:62031:24)
[1m[31mWatch error: Daemon closed the connection[39m[22m
