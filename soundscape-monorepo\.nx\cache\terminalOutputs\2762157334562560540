[2m> [22mvite

[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m

  [32m[1mVITE[22m v5.4.17[39m  [2mready in [0m[1m373[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m4200[22m/[39m
[2m4:09:35 PM[22m [31m[1m[vite][22m[39m Pre-transform error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?
[2m4:09:35 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:09:40 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:23 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:24 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:26 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:28 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:31 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:33 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:10:46 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:17:56 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:17:57 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:18:13 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:18:15 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:18:17 PM[22m [31m[1m[vite][22m[39m [31mInternal server error: Failed to resolve import "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService" from "src/pages/Account.tsx". Does the file exist?[39m
  Plugin: [35mvite:import-analysis[39m
  File: [36mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/apps/soundscape-web/src/pages/Account.tsx[39m:7:63
[33m  22 |    logout
  23 |  } from "@soundscape-monorepo/soundscape-monorepo-redux";
  24 |  import { getUserProfile, updateUserProfile } from "@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService";
     |                                                     ^
  25 |  import { useNavigate } from "react-router-dom";
  26 |  import "./account.css";[39m
      at TransformPluginContext._formatError (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49258:41)
      at TransformPluginContext.error (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49253:16)
      at normalizeUrl (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64229:23)
      at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
      at async file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64361:39
      at async Promise.all (index 5)
      at async TransformPluginContext.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:64288:7)
      at async PluginContainer.transform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:49099:18)
      at async loadAndTransform (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:51938:27)
      at async viteTransformMiddleware (file:///C:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/node_modules/vite/dist/node/chunks/dep-Dyl6b77n.js:62055:24)
[2m4:18:54 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/App.tsx[22m
[2m4:19:08 PM[22m [36m[1m[vite][22m[39m [32mpage reload [39m[2mC:/Users/<USER>/Documents/southernsoundscape/soundscape-monorepo/soundscape-monorepo-redux/src/index.ts[22m
[2m4:19:33 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Account.tsx[22m
[2m4:19:52 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Account.tsx[22m
[2m4:20:11 PM[22m [36m[1m[vite][22m[39m [32mhmr update [39m[2m/src/pages/Account.tsx[22m
^C