{"apps/soundscape-web": {"root": ["apps/soundscape-web/project.json", "nx/core/project-json"], "projectType": ["apps/soundscape-web/project.json", "nx/core/project-json"], "targets": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.dependsOn": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.inputs": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.outputs": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.0": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.example": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.inputs": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.description": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.0": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.example": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.cache": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.inputs": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.outputs": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.options.cwd": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata.technologies": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata.technologies.0": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata.description": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata.help": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata.help.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.test.metadata.help.example": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["apps/soundscape-web/vite.config.ts", "@nx/vite/plugin"], "targets.lint": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.outputs": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/soundscape-web/eslint.config.mjs", "@nx/eslint/plugin"], "name": ["apps/soundscape-web/project.json", "nx/core/project-json"], "$schema": ["apps/soundscape-web/project.json", "nx/core/project-json"], "sourceRoot": ["apps/soundscape-web/project.json", "nx/core/project-json"], "// targets": ["apps/soundscape-web/project.json", "nx/core/project-json"], "tags": ["apps/soundscape-web/project.json", "nx/core/project-json"]}, "soundscape-monorepo-redux": {"root": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "projectType": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "targets": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.dependsOn": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.inputs": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.outputs": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.0": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.example": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.inputs": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.description": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.0": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.example": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["soundscape-monorepo-redux/vite.config.ts", "@nx/vite/plugin"], "targets.lint": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.outputs": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["soundscape-monorepo-redux/eslint.config.mjs", "@nx/eslint/plugin"], "targets.test": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.options": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.cache": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.inputs": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.outputs": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.executor": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.cwd": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.env": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.options.command": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.technologies.0": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.description": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.command": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "targets.test.metadata.help.example": ["soundscape-monorepo-redux/jest.config.ts", "@nx/jest/plugin"], "sourceRoot": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "name": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "tags": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "tags.npm:public": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "metadata.targetGroups": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "metadata.js": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "metadata.js.packageName": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "metadata.js.packageExports": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "targets.nx-release-publish.executor": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["soundscape-monorepo-redux/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "$schema": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "release": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"], "targets.nx-release-publish.options.packageRoot": ["soundscape-monorepo-redux/project.json", "nx/core/project-json"]}, "apps/soundscape-web-e2e": {"root": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "targets": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.cache": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.inputs": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.outputs": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.executor": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.cwd": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.options.command": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.technologies.0": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.description": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.command": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "targets.lint.metadata.help.example": ["apps/soundscape-web-e2e/eslint.config.mjs", "@nx/eslint/plugin"], "metadata.targetGroups": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI)": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).0": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "metadata.targetGroups.E2E (CI).1": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.parallelism": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.cache": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.inputs": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.outputs": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.executor": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.cwd": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.options.command": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.technologies.0": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.description": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.command": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e.metadata.help.example": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.parallelism": ["nx.json", "nx/target-defaults"], "targets.e2e-ci--src/example.spec.ts.metadata": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.cache": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.inputs": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.outputs": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.executor": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.cwd": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.env": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.options.command": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.technologies.0": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.description": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.command": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci--src/example.spec.ts.metadata.help.example": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.executor": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.cache": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.inputs": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.outputs": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.dependsOn": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.parallelism": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.technologies.0": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.description": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.nonAtomizedTarget": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.command": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "targets.e2e-ci.metadata.help.example": ["apps/soundscape-web-e2e/playwright.config.ts", "@nx/playwright/plugin"], "name": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "$schema": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "projectType": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "sourceRoot": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "// targets": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "implicitDependencies": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "implicitDependencies.soundscape-web": ["apps/soundscape-web-e2e/project.json", "nx/core/project-json"], "targets.e2e-ci--src/example.spec.ts.dependsOn": ["nx.json", "nx/target-defaults"]}, ".": {"root": ["project.json", "nx/core/project-json"], "sourceRoot": ["package.json", "nx/core/package-json"], "name": ["project.json", "nx/core/project-json"], "includedScripts": ["package.json", "nx/core/package-json"], "tags": ["package.json", "nx/core/package-json"], "tags.npm:private": ["package.json", "nx/core/package-json"], "metadata.targetGroups": ["package.json", "nx/core/package-json"], "metadata.js": ["package.json", "nx/core/package-json"], "metadata.js.packageName": ["package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["package.json", "nx/core/package-json"], "targets": ["package.json", "nx/core/package-json"], "$schema": ["project.json", "nx/core/project-json"], "targets.local-registry": ["project.json", "nx/core/project-json"], "targets.local-registry.executor": ["project.json", "nx/core/project-json"], "targets.local-registry.options": ["project.json", "nx/core/project-json"], "targets.local-registry.options.port": ["project.json", "nx/core/project-json"], "targets.local-registry.options.config": ["project.json", "nx/core/project-json"], "targets.local-registry.options.storage": ["project.json", "nx/core/project-json"]}}