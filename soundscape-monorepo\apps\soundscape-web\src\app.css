.App {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.app-layout {
  display: flex;
  min-height: calc(100vh - 56px);
  margin-top: 56px;
}

.main-content {
  flex: 1;
  margin-left: 240px;
  margin-bottom: 80px;
  padding: 24px;
  background: var(--bg-primary);
  transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
  margin-left: 72px;
}

/* Hide sidebar on mobile and adjust main content */
@media (max-width: 768px) {
  .main-content,
  .main-content.sidebar-collapsed {
    margin-left: 0;
  }
}