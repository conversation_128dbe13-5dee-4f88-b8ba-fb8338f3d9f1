.music-player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #282828;
  padding: 16px;
  color: white;
}

.music-player-content {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.player-left {
  display: flex;
  align-items: center;
  width: 30%;
}

.album-art {
  width: 56px;
  height: 56px;
  margin-right: 16px;
}

.album-art img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.song-info h3 {
  margin: 0;
  font-size: 14px;
}

.song-info p {
  margin: 4px 0 0;
  font-size: 12px;
  color: #b3b3b3;
}

.player-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
}

.control-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
}

.play-pause {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: white;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-container {
  width: 100%;
  max-width: 600px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #404040;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.progress {
  height: 100%;
  background: #1db954;
  border-radius: 2px;
  transition: width 0.1s linear;
}