import React, { useRef, useEffect } from 'react';
import { 
  useAppSelector, 
  useAppDispatch, 
  pauseSong, 
  resumeSong, 
  updateProgress 
} from '@soundscape-monorepo/soundscape-monorepo-redux';
import './MusicPlayer.css';

const MusicPlayer = () => {
  const dispatch = useAppDispatch();
  const { currentSong, isPlaying, progress } = useAppSelector(state => state.musicPlayer);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.play().catch(error => {
          console.error('Error playing audio:', error);
          dispatch(pauseSong());
        });
      } else {
        audioRef.current.pause();
      }
    }
  }, [isPlaying, currentSong, dispatch]);

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const currentProgress = (audioRef.current.currentTime / audioRef.current.duration) * 100;
      dispatch(updateProgress(currentProgress));
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current) {
      const progressBar = e.currentTarget;
      const clickPosition = (e.clientX - progressBar.getBoundingClientRect().left) / progressBar.offsetWidth;
      audioRef.current.currentTime = clickPosition * audioRef.current.duration;
    }
  };

  if (!currentSong) return null;

  return (
    <div className="music-player">
      <audio 
        ref={audioRef} 
        src={currentSong.url} 
        onEnded={() => dispatch(pauseSong())}
        onTimeUpdate={handleTimeUpdate}
      />
      <div className="music-player-content">
        <div className="player-left">
          <div className="album-art">
            <img src={currentSong.albumArt || '/default-album-art.jpg'} alt="Album Art" />
          </div>
          <div className="song-info">
            <h3>{currentSong.title}</h3>
            <p>{currentSong.artist}</p>
          </div>
        </div>

        <div className="player-center">
          <div className="player-controls">
            <button className="control-button">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z" fill="currentColor"/>
              </svg>
            </button>
            <button 
              className="control-button play-pause" 
              onClick={() => isPlaying ? dispatch(pauseSong()) : dispatch(resumeSong())}
            >
              {isPlaying ? (
                <svg viewBox="0 0 24 24" width="32" height="32">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor"/>
                </svg>
              ) : (
                <svg viewBox="0 0 24 24" width="32" height="32">
                  <path d="M8 5v14l11-7z" fill="currentColor"/>
                </svg>
              )}
            </button>
            <button className="control-button">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill="currentColor"/>
              </svg>
            </button>
          </div>
          <div className="progress-container">
            <div className="progress-bar" onClick={handleProgressClick}>
              <div className="progress" style={{ width: `${progress}%` }}></div>
            </div>
          </div>
        </div>

        <div className="player-right">
          <button className="control-button">
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MusicPlayer;