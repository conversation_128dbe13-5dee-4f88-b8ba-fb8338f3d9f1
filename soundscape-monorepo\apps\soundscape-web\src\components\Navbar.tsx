import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector, logout } from '@soundscape-monorepo/soundscape-monorepo-redux';
import './navbar.css';

interface NavbarProps {
  onToggleSidebar: () => void;
  onToggleMobileDrawer: () => void;
  isMobileDrawerOpen: boolean;
  onCloseMobileDrawer: () => void;
}

const Navbar: React.FC<NavbarProps> = ({
  onToggleSidebar,
  onToggleMobileDrawer,
  isMobileDrawerOpen,
  onCloseMobileDrawer
}) => {
  const { isAuthenticated } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleMenuClick = () => {
    // On desktop: toggle sidebar collapse
    // On mobile: toggle mobile drawer
    if (window.innerWidth > 768) {
      onToggleSidebar();
    } else {
      onToggleMobileDrawer();
    }
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout());
      navigate('/login');
      onCloseMobileDrawer();
    } catch (error) {
      console.error('Logout failed:', error);
      // Still navigate to login even if logout API fails
      navigate('/login');
      onCloseMobileDrawer();
    }
  };

  return (
    <>
      <header className="header">
        <div className="header-left">
          <button className="menu-button" onClick={handleMenuClick}>
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" fill="currentColor"/>
            </svg>
          </button>
          <div className="header-logo">
            <img src="/assets/logoforSoundscape.jpg" alt="SoundScape Logo" className="header-logo-image" />
          </div>
        </div>

        <div className="search-bar">
          <form>
            <input type="text" placeholder="Search" />
            <button type="submit">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" fill="currentColor"/>
              </svg>
            </button>
          </form>
        </div>

        <div className="header-right">
          {isAuthenticated ? (
            <>
              <Link to="/account" className="icon-button">
                <svg viewBox="0 0 24 24" width="24" height="24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" fill="currentColor"/>
                </svg>
              </Link>
              <button onClick={handleLogout} className="icon-button">
                <svg viewBox="0 0 24 24" width="24" height="24">
                  <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" fill="currentColor"/>
                </svg>
              </button>
            </>
          ) : (
            <Link to="/login" className="icon-button">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" fill="currentColor"/>
              </svg>
            </Link>
          )}
        </div>
      </header>

      {/* Mobile drawer */}
      <div className={`drawer ${isMobileDrawerOpen ? 'drawer-open' : ''}`}>
        <div className="drawer-content">
          <div className="drawer-header">
            <img src="/assets/logoforSoundscape.jpg" alt="SoundScape Logo" className="drawer-logo-image" />
          </div>
          <Link to="/" className="drawer-item" onClick={onCloseMobileDrawer}>
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
            </svg>
            <span>Home</span>
          </Link>
          <Link to="/explore" className="drawer-item" onClick={onCloseMobileDrawer}>
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M12 10.9c-.61 0-1.1.49-1.1 1.1s.49 1.1 1.1 1.1c.61 0 1.1-.49 1.1-1.1s-.49-1.1-1.1-1.1zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm2.19 12.19L6 18l3.81-8.19L18 6l-3.81 8.19z" fill="currentColor"/>
            </svg>
            <span>Explore</span>
          </Link>
          <Link to="/genres" className="drawer-item" onClick={onCloseMobileDrawer}>
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
            </svg>
            <span>Library</span>
          </Link>
          <Link to="/about" className="drawer-item" onClick={onCloseMobileDrawer}>
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
            </svg>
            <span>About</span>
          </Link>
          {isAuthenticated && (
            <Link to="/account" className="drawer-item" onClick={onCloseMobileDrawer}>
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" fill="currentColor"/>
              </svg>
              <span>Account</span>
            </Link>
          )}
          {isAuthenticated && (
            <button onClick={handleLogout} className="drawer-item">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" fill="currentColor"/>
              </svg>
              <span>Logout</span>
            </button>
          )}
        </div>
      </div>

      {/* Overlay */}
      {isMobileDrawerOpen && <div className="overlay" onClick={onCloseMobileDrawer}></div>}
    </>
  );
};

export default Navbar;

