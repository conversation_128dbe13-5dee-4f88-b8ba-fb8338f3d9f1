import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import ThemeToggle from './ThemeToggle';
import './sidebar.css';

interface SidebarProps {
  isCollapsed: boolean;
  isMobileOpen: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, isMobileOpen }) => {
  const location = useLocation();

  return (
    <nav className={`sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobileOpen ? 'mobile-open' : ''}`}>
      <div className="sidebar-section">
        <Link to="/" className={`sidebar-item ${location.pathname === '/' ? 'active' : ''}`} title="Home">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>Home</span>}
        </Link>
        <Link to="/explore" className={`sidebar-item ${location.pathname === '/explore' ? 'active' : ''}`} title="Explore">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 10.9c-.61 0-1.1.49-1.1 1.1s.49 1.1 1.1 1.1c.61 0 1.1-.49 1.1-1.1s-.49-1.1-1.1-1.1zM12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm2.19 12.19L6 18l3.81-8.19L18 6l-3.81 8.19z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>Explore</span>}
        </Link>
        <Link to="/genres" className={`sidebar-item ${location.pathname.startsWith('/genres') ? 'active' : ''}`} title="Genres">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>Genres</span>}
        </Link>
      </div>

     <div className="sidebar-divider"></div>

      <div className="sidebar-section">
        <Link to="/about" className={`sidebar-item ${location.pathname === '/about' ? 'active' : ''}`} title="About">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
          </svg>
          {!isCollapsed && <span>About</span>}
        </Link>
      </div>
      <div className="theme-section">
        <ThemeToggle isCollapsed={isCollapsed} />
      </div>
    </nav>
  );
};

export default Sidebar;