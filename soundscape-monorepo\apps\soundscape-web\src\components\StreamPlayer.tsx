import React, { useEffect, useState, useCallback } from 'react';
import {
  useAppDispatch,
  useAppSelector,
  startChannelStreaming,
  playTrack,
  pauseTrack,
  nextTrack,
  fetchPlaylist,
  setProgress,
  skipToNextTrack,
  getTrackInfo
} from '@soundscape-monorepo/soundscape-monorepo-redux';
import './StreamPlayer.css';

// Helper function to format time in MM:SS format
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

interface StreamPlayerProps {
  channelId: string;
}

const StreamPlayer: React.FC<StreamPlayerProps> = ({ channelId }) => {
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const {
    isPlaying,
    currentTrack,
    playlist,
    streamUrl,
    loading: playlistLoading,
    error,
    progress,
    duration
  } = useAppSelector((state) => state.player);

  // Start streaming when component mounts or when track changes
  useEffect(() => {
    if (channelId && playlist.length > 0) {
      setIsLoading(true);
      dispatch(startChannelStreaming(channelId))
        .unwrap()
        .then(() => {
          console.log('Stream started successfully');
          // Auto-play when stream is ready
          dispatch(playTrack());
        })
        .catch((error) => {
          console.error('Failed to start stream:', error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [channelId, currentTrack, playlist, dispatch]);

  // Auto-play when stream URL is available
  useEffect(() => {
    if (streamUrl && !isPlaying) {
      dispatch(playTrack());
    }
  }, [streamUrl, isPlaying, dispatch]);

  const handlePlayPause = useCallback(() => {
    if (isPlaying) {
      dispatch(pauseTrack());
    } else {
      dispatch(playTrack());
    }
  }, [isPlaying, dispatch]);

  const handleSkip = useCallback(() => {
    if (currentTrack >= playlist.length - 1) {
      return; // Already at the last track
    }

    setIsLoading(true);

    // First update the UI immediately for better responsiveness
    dispatch(nextTrack());

    // Then tell the API to skip to the next track
    dispatch(skipToNextTrack({ channelId, currentTrack }))
      .unwrap()
      .then((nextTrackIndex) => {
        console.log(`Skipped to track ${nextTrackIndex}`);
        // The streaming will be handled by the useEffect that watches currentTrack
      })
      .catch((error) => {
        console.error('Failed to skip track:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [dispatch, channelId, currentTrack, playlist.length]);

  const handleRetry = useCallback(() => {
    setIsLoading(true);
    dispatch(fetchPlaylist(channelId))
      .unwrap()
      .then(() => {
        console.log('Playlist refreshed successfully');
      })
      .catch((error) => {
        console.error('Failed to refresh playlist:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [channelId, dispatch]);

  // Get current track info
  const currentTrackData = playlist[currentTrack] || null;
  const trackTitle = currentTrackData?.title || 'Unknown Track';
  const artistName = currentTrackData?.artists?.[0]?.name || 'Unknown Artist';
  const albumTitle = currentTrackData?.albumTitle || '';
  const albumImage = currentTrackData?.albumImage || '/default-album-art.jpg';

  if (playlistLoading) {
    return <div className="stream-player loading">Loading stream...</div>;
  }

  if (error) {
    return (
      <div className="stream-player error">
        <p>Error: {error}</p>
        <button className="retry-button" onClick={handleRetry}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="stream-player">
      <div className="track-display">
        <div className="album-image">
          <img src={albumImage} alt="Album Cover" />
        </div>
        <div className="stream-info">
          <h3>{trackTitle}</h3>
          <p>{artistName}</p>
          {albumTitle && (
            <p className="album-title">{albumTitle}</p>
          )}
        </div>
      </div>

      <div className="stream-controls">
        <button
          className="control-button play-pause"
          onClick={handlePlayPause}
          disabled={isLoading}
          title={isPlaying ? 'Pause' : 'Play'}
        >
          {isLoading ? (
            <span className="loading-indicator">Loading...</span>
          ) : isPlaying ? (
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor" />
            </svg>
          ) : (
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path d="M8 5v14l11-7z" fill="currentColor" />
            </svg>
          )}
        </button>

        <button
          className="control-button skip"
          onClick={handleSkip}
          disabled={isLoading || currentTrack >= playlist.length - 1}
          title="Skip to next track"
        >
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill="currentColor" />
          </svg>
        </button>
      </div>

      {isPlaying && (
        <div className="now-playing-indicator">
          <div className="playing-dot"></div>
          <div className="playing-dot"></div>
          <div className="playing-dot"></div>
        </div>
      )}

      <div className="progress-bar-container">
        <div
          className="progress-bar"
          onClick={(e) => {
            if (duration) {
              const progressBar = e.currentTarget;
              const clickPosition = (e.clientX - progressBar.getBoundingClientRect().left) / progressBar.offsetWidth;
              const newTime = clickPosition * duration;
              dispatch(setProgress(newTime));
            }
          }}
        >
          <div
            className="progress-fill"
            style={{ width: `${duration ? (progress / duration) * 100 : 0}%` }}
          ></div>
        </div>
        <div className="progress-time">
          {formatTime(progress)} / {formatTime(duration)}
        </div>
      </div>

      <div className="track-position">
        <span>Track {currentTrack + 1} of {playlist.length}</span>
      </div>
    </div>
  );
};

export default StreamPlayer;
