import { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { loadSelectedGenre, clearSelectedGenre } from '../utils/genrePersistence';

/**
 * Hook to handle genre persistence and restoration
 * This hook will:
 * 1. Only redirect on initial page load (not on navigation)
 * 2. Check if we're on the genres list page and there's a persisted genre
 * 3. Clear persisted genre when explicitly navigating away from a genre
 */
export const useGenrePersistence = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const hasRedirected = useRef(false);
  const isInitialLoad = useRef(true);

  useEffect(() => {
    // Only check for restoration on the genres list page
    if (location.pathname === '/genres') {
      // Only redirect on initial page load, not on navigation
      // Check if this is the initial load by seeing if we haven't redirected yet
      // and if the user came directly to /genres (not from navigation)
      const persistedGenre = loadSelectedGenre();

      if (persistedGenre && isInitialLoad.current && !hasRedirected.current) {
        // Check if this is a direct page load (not navigation from within the app)
        // We can detect this by checking if there's a navigation state
        const isDirectLoad = !location.state;

        if (isDirectLoad) {
          console.log(`🎵 Initial page load detected with persisted genre ${persistedGenre.genreId}, redirecting...`);
          hasRedirected.current = true;
          navigate(`/genres/${persistedGenre.genreId}`, { replace: true });
        } else {
          console.log(`🎵 Navigation to genres detected, not redirecting to persisted genre`);
        }
      }
    }

    // Mark that we've completed the initial load check
    isInitialLoad.current = false;
  }, [location.pathname, location.state, navigate]);

  return {
    clearPersistence: clearSelectedGenre
  };
};
