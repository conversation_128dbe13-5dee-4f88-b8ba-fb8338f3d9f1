import { StrictMode } from 'react';
import * as ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';

// Import store as named import
import { store } from '@soundscape-monorepo/soundscape-monorepo-redux';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <StrictMode>
    <Provider store={store}>
      <Router>
        <App />
      </Router>
    </Provider>
  </StrictMode>
);
