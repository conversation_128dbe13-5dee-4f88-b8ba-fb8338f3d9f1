import React, { useState } from 'react';
import { 
  useAppSelector, 
  useAppDispatch, 
  updateUserName, 
  updateUserAge 
} from '@soundscape-monorepo/soundscape-monorepo-redux';
import './account.css';

const Account = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.user);
  
  const [nameInput, setNameInput] = useState(user.name);
  const [ageInput, setAgeInput] = useState(user.age.toString());

  const handleUpdateUser = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(updateUserName(nameInput));
    dispatch(updateUserAge(Number(ageInput)));
  };

  return (
    <div className="account-page">
      <h1>Account Settings</h1>
      
      <div className="account-card">
        <h2>User Profile</h2>
        
        <div className="user-details">
          <div className="avatar">
            <svg viewBox="0 0 24 24" width="80" height="80">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" fill="currentColor"/>
            </svg>
          </div>
          
          <div className="user-info">
            <h3>{user.name}</h3>
            <p>Age: {user.age}</p>
            <p>Member since: January 2023</p>
          </div>
        </div>
        
        <form onSubmit={handleUpdateUser} className="user-form">
          <h3>Edit Profile</h3>
          
          <div className="form-group">
            <label htmlFor="name">Name:</label>
            <input 
              type="text" 
              id="name" 
              value={nameInput} 
              onChange={(e) => setNameInput(e.target.value)} 
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="age">Age:</label>
            <input 
              type="number" 
              id="age" 
              value={ageInput} 
              onChange={(e) => setAgeInput(e.target.value)} 
            />
          </div>
          
          <button type="submit" className="update-button">Update Profile</button>
        </form>
      </div>
    </div>
  );
};

export default Account; 