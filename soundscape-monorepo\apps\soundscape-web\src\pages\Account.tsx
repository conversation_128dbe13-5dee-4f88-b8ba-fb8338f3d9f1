import React, { useState, useEffect } from 'react';
import {
  useAppSelector,
  useAppDispatch,
  logout
} from '@soundscape-monorepo/soundscape-monorepo-redux';
import { getUserProfile, updateUserProfile, UserProfile } from '@soundscape-monorepo/soundscape-monorepo-redux/src/services/userService';
import { useNavigate } from 'react-router-dom';
import './account.css';

const Account = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAppSelector(state => state.auth);

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState('');
  const [nameInput, setNameInput] = useState('');
  const [descriptionInput, setDescriptionInput] = useState('');
  const [phoneInput, setPhoneInput] = useState('');

  // Load user profile on component mount
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const loadProfile = async () => {
      try {
        setLoading(true);
        const userProfile = await getUserProfile();
        setProfile(userProfile);
        setNameInput(userProfile.name);
        setDescriptionInput(userProfile.description || '');
        setPhoneInput(userProfile.phone || '');
      } catch (err) {
        console.error('Failed to load profile:', err);
        setError('Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    loadProfile();
  }, [isAuthenticated, navigate]);

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setUpdating(true);

    try {
      const updates = {
        name: nameInput,
        description: descriptionInput,
        phone: phoneInput
      };

      const updatedProfile = await updateUserProfile(updates);
      setProfile(updatedProfile);
      setError('');
    } catch (err) {
      console.error('Failed to update profile:', err);
      setError('Failed to update profile');
    } finally {
      setUpdating(false);
    }
  };

  const handleLogout = async () => {
    await dispatch(logout());
    navigate('/login');
  };

  if (loading) {
    return (
      <div className="account-page">
        <h1>Account Settings</h1>
        <div className="account-card">
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="account-page">
      <h1>Account Settings</h1>

      {error && <div className="error-message">{error}</div>}

      <div className="account-card">
        <div className="account-header">
          <h2>User Profile</h2>
          <button onClick={handleLogout} className="logout-button">
            Logout
          </button>
        </div>

        <div className="user-details">
          <div className="avatar">
            <svg viewBox="0 0 24 24" width="80" height="80">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z" fill="currentColor"/>
            </svg>
          </div>

          <div className="user-info">
            <h3>{profile?.name || user?.name}</h3>
            <p>Email: {profile?.email || user?.email}</p>
            <p>Member since: {profile?.created_date ? new Date(profile.created_date).toLocaleDateString() : 'Unknown'}</p>
            {profile?.description && <p>Description: {profile.description}</p>}
          </div>
        </div>
        
        <form onSubmit={handleUpdateUser} className="user-form">
          <h3>Edit Profile</h3>

          <div className="form-group">
            <label htmlFor="name">Name:</label>
            <input
              type="text"
              id="name"
              value={nameInput}
              onChange={(e) => setNameInput(e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description:</label>
            <textarea
              id="description"
              value={descriptionInput}
              onChange={(e) => setDescriptionInput(e.target.value)}
              placeholder="Tell us about yourself..."
              rows={3}
            />
          </div>

          <div className="form-group">
            <label htmlFor="phone">Phone:</label>
            <input
              type="tel"
              id="phone"
              value={phoneInput}
              onChange={(e) => setPhoneInput(e.target.value)}
              placeholder="******-0123"
            />
          </div>

          <button type="submit" className="update-button" disabled={updating}>
            {updating ? 'Updating...' : 'Update Profile'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Account; 