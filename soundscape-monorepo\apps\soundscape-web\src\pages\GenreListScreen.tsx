import React, { useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import './genrelistscreen.css';

// Import from Redux package
import {
  fetchGenresAsync,
  useAppDispatch,
  setSelectedGenre,
  Genre,
  GenreState
} from '@soundscape-monorepo/soundscape-monorepo-redux';

// Import persistence utilities
import { clearSelectedGenre } from '../utils/genrePersistence';
import { useGenrePersistence } from '../hooks/useGenrePersistence';

// Mock data for featured collections (not from API for now)
const featuredCollections = [
  {
    id: 1,
    name: 'Workout Mix',
    description: 'High energy tracks for your workout',
    color: '#3a7bd5',
    icon: '🎧'
  },
  {
    id: 2,
    name: 'Late Night Vibes',
    description: 'Chill beats for after dark',
    color: '#8e44ad',
    icon: '🌙'
  },
  {
    id: 3,
    name: 'Study Focus',
    description: 'Concentration enhancing sounds',
    color: '#16a085',
    icon: '📚'
  }
];

// Type definition for genre
interface GenreItem {
  id: number;
  name: string;
  color: string;
  icon: string;
}

// Define GenresState locally since we can't import it
interface GenreListState {
  items: GenreItem[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null;
}

const GenreListScreen = () => {
  const dispatch = useAppDispatch();

  // Use genre persistence hook
  useGenrePersistence();

  // Use the GenreState type from our exports
  const genreState = useSelector((state: any) => state.genres as GenreState);

  const genres = genreState?.items || [];
  const loading = genreState?.loading || false;
  const error = genreState?.error || null;

  // Fetch genres handler
  const loadGenres = useCallback(() => {
    dispatch(fetchGenresAsync());
  }, [dispatch]);

  // Fetch on component mount and clear selected genre from Redux only
  useEffect(() => {
    // Clear Redux selected genre when on the list (but keep localStorage for persistence)
    console.log('🎵 Clearing Redux selected genre on genre list screen');
    dispatch(setSelectedGenre(null));

    loadGenres();
  }, [loadGenres, dispatch]);

  // Helper function to darken/lighten colors for gradients
  const adjustColor = (color: string, amount: number): string => {
    return color; // Simplified for now
  };

  if (loading && genres.length === 0) {
    return (
      <div className="genrelist-page">
        <div className="genrelist-loading">
          <p>Loading genres...</p>
        </div>
      </div>
    );
  }

  if (error && genres.length === 0) {
    return (
      <div className="genrelist-page">
        <div className="genrelist-error">
          <p>Error: {error}</p>
          <button onClick={loadGenres}>Try Again</button>
        </div>
      </div>
    );
  }

  return (
    <div className="genrelist-page">
      <div className="genrelist-header">
        <h1>Explore Genres</h1>
        <p>Discover music by your favorite genres</p>
        {loading && <small className="refresh-indicator">(Refreshing...)</small>}
        {error && <small className="error-indicator">Error refreshing: {error}</small>}
        <button
          onClick={loadGenres}
          style={{ padding: '8px 12px', marginTop: '10px', cursor: 'pointer' }}
        >
          Manual Refresh Genres
        </button>
      </div>

      <div className="genrelist-grid">
        {genres.length === 0 ? (
          <div className="no-genres">No genres available. Please try again later.</div>
        ) : (
          genres.map((genre: Genre) => (
            <Link
              to={`/genres/${genre.id}`}
              key={genre.id}
              className="genre-tile"
              style={{
                backgroundColor: genre.color,
                backgroundImage: `linear-gradient(45deg, ${genre.color}, ${adjustColor(genre.color, -30)})`
              }}
            >
              <div className="genre-icon">{genre.icon}</div>
              <h3>{genre.name}</h3>
            </Link>
          ))
        )}
      </div>

      <div className="genre-featured">
        <h2>Featured Genre Collections</h2>
        <div className="featured-cards">
          {featuredCollections.map(collection => (
            <div className="featured-card" key={collection.id}>
              <div className="featured-image" style={{ backgroundColor: collection.color }}>
                <span>{collection.icon}</span>
              </div>
              <h3>{collection.name}</h3>
              <p>{collection.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default GenreListScreen;