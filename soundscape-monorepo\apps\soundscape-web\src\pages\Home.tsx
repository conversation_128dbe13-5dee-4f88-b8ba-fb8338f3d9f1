import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import {
  useAppSelector,
  useAppDispatch,
  playSong,
  fetchGenres,
  fetchPlaylist
} from '@soundscape-monorepo/soundscape-monorepo-redux';
import StreamPlayer from '../components/StreamPlayer';
import './home.css';

interface Genre {
  id: number;
  name: string;
  color: string;
  icon: string;
}

interface GenreListState {
  items: Genre[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null;
}

const Home = () => {
  const dispatch = useAppDispatch();
  const genreListState = useAppSelector((state: any) => state.genres as GenreListState);
  const playerState = useAppSelector((state: any) => state.player);
  const genres = genreListState?.items || [];
  const loading = genreListState?.loading || false;
  const error = genreListState?.error || null;
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);

  useEffect(() => {
    dispatch(fetchGenres());
  }, [dispatch]);

  const handlePlayClick = () => {
    console.log('handlePlayClick');
    dispatch(playSong({
      id: '1',
      title: 'Rock and Roll All Nite',
      artist: 'The Rock Band',
      url: "https://cdn.freesound.org/previews/612/612095_5674468-lq.mp3",
      albumArt: "https://images.unsplash.com/photo-1498038432885-c6f3f1b912ee?q=80&w=2070"
    }));
  };

  const handleChannelClick = (channelId: string) => {
    dispatch(fetchPlaylist(channelId));
    setSelectedChannel(channelId);
  };

  return (
    <div className="home">
      <h1>Welcome to SoundScape</h1>
      <p>Discover and share amazing sounds</p>
      <section className="test-players-section">
        <h2>Test Audio Player</h2>
        <div className="player-test-buttons">
          <div>
            <h3>Direct URL Player:</h3>
            <button onClick={handlePlayClick} className="player-test-button">
              Play Direct Song (MusicPlayer)
            </button>
          </div>

          <div>
            <h3>Channel Streaming:</h3>
            <button
              onClick={() => handleChannelClick('1')}
              className="player-test-button channel-button"
            >
              Play Rock Classics Channel
            </button>
            <button
              onClick={() => handleChannelClick('2')}
              className="player-test-button channel-button"
            >
              Play Jazz Hits Channel
            </button>
            <button
              onClick={() => handleChannelClick('3')}
              className="player-test-button channel-button"
            >
              Play Pop Favorites Channel
            </button>
          </div>
        </div>
        <p className="note">Note: You can test both player modes to ensure AudioPlayer handles them correctly.</p>
      </section>

      <section className="channels-section">
        <h2>Featured Channels</h2>
        {playerState.loading && <p>Loading channels...</p>}
        {playerState.error && <p className="error">{playerState.error}</p>}

        <div className="channel-grid">
          <button onClick={() => handleChannelClick('1')}>
            Rock Classics
          </button>
          <button onClick={() => handleChannelClick('2')}>
            Jazz Hits
          </button>
          <button onClick={() => handleChannelClick('3')}>
            Pop Favorites
          </button>
        </div>
      </section>

      <section className="streaming-section">
        <h2>Stream Player</h2>
        <p>Listen to streaming music and skip to the next track</p>

        <div className="streaming-container">
          <div className="channel-selection">
            <h3>Select a Channel</h3>
            <div className="channel-buttons">
              <button
                className={`channel-button ${selectedChannel === '1' ? 'active' : ''}`}
                onClick={() => handleChannelClick('1')}
              >
                <span className="channel-icon" role="img" aria-label="Electric Guitar">🎸</span>
                Rock Classics
              </button>
              <button
                className={`channel-button ${selectedChannel === '2' ? 'active' : ''}`}
                onClick={() => handleChannelClick('2')}
              >
                <span className="channel-icon" role="img" aria-label="Saxophone">🎷</span>
                Jazz Hits
              </button>
              <button
                className={`channel-button ${selectedChannel === '3' ? 'active' : ''}`}
                onClick={() => handleChannelClick('3')}
              >
                <span className="channel-icon" role="img" aria-label="Musical Note">🎵</span>
                Pop Favorites
              </button>
            </div>
          </div>

          <div className="player-container">
            {playerState.loading ? (
              <div className="loading-indicator">
                <p>Loading channel...</p>
              </div>
            ) : selectedChannel && playerState.playlist.length > 0 ? (
              <StreamPlayer channelId={selectedChannel} />
            ) : (
              <div className="stream-player-placeholder">
                <p>Select a channel to start streaming</p>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;