import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import './selectedgenre.css';

// Import from Redux package
import {
  useAppDispatch,
  fetchGenreChannels,
  fetchGenresAsync,
  setSelectedGenre,
  Genre,
  Channel,
  GenreState,
  startChannelStreaming,
  fetchPlaylist
} from '@soundscape-monorepo/soundscape-monorepo-redux';

// Import persistence utilities
import { saveSelectedGenre, loadSelectedGenre, clearSelectedGenre } from '../utils/genrePersistence';

// Mock tracks data - in a real app, this would come from an API
const mockTracks = [
  { id: 1, title: 'Summer Breeze', artist: 'Melody Masters', duration: '3:45', popular: true },
  { id: 2, title: 'Midnight Drive', artist: 'The Journeymen', duration: '4:12', popular: true },
  { id: 3, title: 'Mountain High', artist: 'Altitude', duration: '3:22', popular: false },
  { id: 4, title: 'Ocean Waves', artist: 'Coastal Sound', duration: '5:07', popular: true },
  { id: 5, title: 'City Lights', artist: 'Urban Echo', duration: '3:56', popular: false },
  { id: 6, title: 'Desert Wind', artist: 'Sand Nomads', duration: '4:33', popular: false },
  { id: 7, title: 'Forest Rain', artist: 'Nature Sounds', duration: '6:21', popular: true },
  { id: 8, title: 'Starry Night', artist: 'Cosmic Band', duration: '4:45', popular: false }
];

// Mock artists for this genre
const mockArtists = [
  { id: 1, name: 'Melody Masters', followers: '2.3M', image: '👨‍🎤' },
  { id: 2, name: 'The Journeymen', followers: '1.8M', image: '👩‍🎤' },
  { id: 3, name: 'Altitude', followers: '950K', image: '🧑‍🎤' },
  { id: 4, name: 'Coastal Sound', followers: '1.5M', image: '👨‍🎤' },
  { id: 5, name: 'Urban Echo', followers: '750K', image: '👩‍🎤' }
];

const SelectedGenre = () => {
  const { genreId } = useParams<{ genreId: string }>();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Get genre data from Redux store
  const genreState = useSelector((state: any) => state.genres as GenreState);
  const allGenres = genreState?.items || [];
  const genresLoading = genreState?.loading || false;
  const genresError = genreState?.error || null;
  const channels = genreState.channelsById[genreId || ''] || [];
  const channelsLoading = genreState.channelsLoading;
  const channelsError = genreState.channelsError;

  // Find the selected genre based on the ID from URL params
  const selectedGenre = allGenres.find(genre => genre.id === Number(genreId));

  // Local state for loading and error handling
  const [isInitializing, setIsInitializing] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  // Setting up state for tracks
  const [popularTracks, setPopularTracks] = useState(mockTracks.filter(track => track.popular));
  const [allTracks, setAllTracks] = useState(mockTracks);

  // Initialize the component - fetch genres if not loaded, then fetch channels
  useEffect(() => {
    const initializeGenreView = async () => {
      console.log(`🎵 Initializing genre view for ID: ${genreId}`);

      if (!genreId) {
        console.log('❌ No genre ID provided, redirecting to genres list');
        navigate('/genres');
        return;
      }

      try {
        setIsInitializing(true);
        setInitError(null);

        // If genres are not loaded or are stale, fetch them first
        const shouldFetchGenres = allGenres.length === 0 ||
          !genreState.lastFetched ||
          (Date.now() - genreState.lastFetched > 5 * 60 * 1000); // 5 minutes

        if (shouldFetchGenres) {
          console.log('🎵 Fetching genres list...');
          await dispatch(fetchGenresAsync()).unwrap();
        }

        // Set the selected genre in Redux state
        dispatch(setSelectedGenre(Number(genreId)));

        // Fetch channels for this genre
        console.log(`🎵 Fetching channels for genre ${genreId}...`);
        await dispatch(fetchGenreChannels(genreId)).unwrap();

        // Save to localStorage for persistence
        const genre = allGenres.find(g => g.id === Number(genreId));
        if (genre) {
          saveSelectedGenre(genre.id, genre.name);
        }

        console.log('✅ Genre view initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize genre view:', error);
        setInitError(error instanceof Error ? error.message : 'Failed to load genre data');
      } finally {
        setIsInitializing(false);
      }
    };

    initializeGenreView();
  }, [genreId, dispatch, navigate, allGenres.length, genreState.lastFetched]);

  // Validate that the genre exists after genres are loaded
  useEffect(() => {
    if (!isInitializing && !genresLoading && allGenres.length > 0 && genreId) {
      const genre = allGenres.find(g => g.id === Number(genreId));
      if (!genre) {
        console.log(`❌ Genre with ID ${genreId} not found, redirecting to genres list`);
        navigate('/genres');
      }
    }
  }, [isInitializing, genresLoading, allGenres, genreId, navigate]);

  // Handle channel click to start streaming
  const handleChannelClick = async (channel: Channel) => {
    console.log('Starting streaming for channel:', channel.name, 'ID:', channel.id);

    try {
      // First fetch the playlist to get track information
      await dispatch(fetchPlaylist(channel.id.toString())).unwrap();

      // Then start the streaming
      await dispatch(startChannelStreaming(channel.id.toString())).unwrap();

      console.log('Successfully started streaming for channel:', channel.name);
    } catch (error) {
      console.error('Failed to start streaming:', error);
    }
  };

  // Handle back to genres with explicit clearing
  const handleBackToGenres = () => {
    console.log('🎵 User explicitly navigating back to genres, clearing persistence');
    clearSelectedGenre();
    navigate('/genres', { state: { fromGenreView: true } });
  };

  // Show loading state while initializing
  if (isInitializing || genresLoading) {
    return (
      <div className="selected-genre-page">
        <div className="genre-header">
          <Link to="/genres" className="back-button">
            ← Back to Genres
          </Link>
          <div className="genre-header-content">
            <h1>Loading...</h1>
            <p>Loading genre information...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if initialization failed
  if (initError || genresError) {
    return (
      <div className="selected-genre-page">
        <div className="genre-header">
          <button onClick={handleBackToGenres} className="back-button">
            ← Back to Genres
          </button>
          <div className="genre-header-content">
            <h1>Error</h1>
            <p>{initError || genresError || 'Failed to load genre data'}</p>
            <button
              onClick={() => window.location.reload()}
              style={{
                marginTop: '10px',
                padding: '8px 16px',
                backgroundColor: 'var(--accent-color)',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If we can't find the genre, show a not found message (after loading is complete)
  if (!selectedGenre) {
    return (
      <div className="selected-genre-page">
        <div className="genre-header">
          <button onClick={handleBackToGenres} className="back-button">
            ← Back to Genres
          </button>
          <div className="genre-header-content">
            <h1>Genre Not Found</h1>
            <p>The requested genre could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="selected-genre-page">
      <div
        className="genre-header"
        style={{
          backgroundColor: selectedGenre.color,
          backgroundImage: `linear-gradient(to bottom, ${selectedGenre.color}, ${selectedGenre.color}88)`
        }}
      >
        <button onClick={handleBackToGenres} className="back-button">
          ← Back to Genres
        </button>
        <div className="genre-header-content">
          <div className="genre-icon">{selectedGenre.icon}</div>
          <h1>{selectedGenre.name}</h1>
          <p>Explore the best of {selectedGenre.name} music</p>
        </div>
      </div>

      <div className="genre-content">
        {/* Channels section from API */}
        <section className="genre-channels">
          <h2>Channels</h2>
          {channelsLoading && <p>Loading channels...</p>}
          {channelsError && <p className="error">Error loading channels: {channelsError}</p>}
          <div className="channels-grid">
            {channels.map((channel: Channel) => (
              <div
                className="channel-card clickable"
                key={channel.id}
                onClick={() => handleChannelClick(channel)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleChannelClick(channel);
                  }
                }}
              >
                <div className="channel-image">
                  {channel.imageUrl ? (
                    <img src={channel.imageUrl} alt={channel.name} />
                  ) : (
                    <div className="placeholder-image">📻</div>
                  )}
                  <div className="play-overlay">
                    <div className="play-button">▶️</div>
                  </div>
                </div>
                <h3>{channel.name}</h3>
                <p>{channel.description || `${selectedGenre.name} channel`}</p>
                <div className="channel-actions">
                  <span className="stream-hint">Click to stream</span>
                </div>
              </div>
            ))}
            {!channelsLoading && channels.length === 0 && !channelsError && (
              <p>No channels found for this genre.</p>
            )}
          </div>
        </section>

        <section className="genre-popular-tracks">
          <h2>Popular Tracks</h2>
          <div className="tracks-list">
            {popularTracks.map(track => (
              <div className="track-item" key={track.id}>
                <div className="track-info">
                  <h3>{track.title}</h3>
                  <p>{track.artist}</p>
                </div>
                <div className="track-duration">{track.duration}</div>
              </div>
            ))}
          </div>
        </section>

        <section className="genre-featured-artists">
          <h2>Featured Artists</h2>
          <div className="artists-grid">
            {mockArtists.map(artist => (
              <div className="artist-card" key={artist.id}>
                <div className="artist-image">{artist.image}</div>
                <h3>{artist.name}</h3>
                <p>{artist.followers} followers</p>
              </div>
            ))}
          </div>
        </section>

        <section className="genre-all-tracks">
          <h2>All Tracks</h2>
          <div className="tracks-list">
            {allTracks.map(track => (
              <div className="track-item" key={track.id}>
                <div className="track-info">
                  <h3>{track.title}</h3>
                  <p>{track.artist}</p>
                </div>
                <div className="track-duration">{track.duration}</div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default SelectedGenre;