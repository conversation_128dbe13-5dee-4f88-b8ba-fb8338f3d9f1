.account-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.account-page h1 {
  margin-bottom: 2rem;
  font-size: 2rem;
  color: #333;
}

.account-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.account-card h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.logout-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: #c82333;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.user-details {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.avatar {
  margin-right: 1.5rem;
  color: #4a90e2;
  background-color: #f5f8ff;
  border-radius: 50%;
  padding: 0.5rem;
}

.user-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.user-info p {
  margin: 0.3rem 0;
  color: #666;
}

.user-form {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 6px;
}

.user-form h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: #333;
}

.form-group {
  margin-bottom: 1.2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  font-family: inherit;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.update-button {
  padding: 0.8rem 1.5rem;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.update-button:hover {
  background-color: #3a7bc8;
} 