.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.auth-card {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 400px;
}

.auth-card h1 {
  margin: 0 0 8px;
  font-size: 2rem;
  color: #333;
  text-align: center;
}

.auth-subtitle {
  color: #666;
  text-align: center;
  margin-bottom: 24px;
}

.auth-error {
  background: #fed7d7;
  color: #c53030;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #feb2b2;
  font-size: 0.9rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: #444;
}

.form-group input {
  padding: 10px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #3182ce;
}

.auth-button {
  background: #3182ce;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 8px;
}

.auth-button:hover:not(:disabled) {
  background: #2c5282;
}

.auth-button:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  opacity: 0.6;
}

.auth-links {
  margin-top: 24px;
  text-align: center;
}

.forgot-password {
  color: #3182ce;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

.signup-prompt,
.login-prompt {
  margin-top: 16px;
  color: #666;
}

.signup-prompt a,
.login-prompt a {
  color: #3182ce;
  text-decoration: none;
  font-weight: 500;
}

.signup-prompt a:hover,
.login-prompt a:hover {
  text-decoration: underline;
} 