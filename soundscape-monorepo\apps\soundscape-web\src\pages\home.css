.home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.home h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  color: #333;
}

.home p {
  color: #666;
  margin-bottom: 30px;
}

.genres-section {
  margin: 30px 0;
}

.genres-scroll {
  display: flex;
  gap: 15px;
  overflow-x: auto;
  padding: 10px 0;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #888 #f0f0f0;
}

.genres-scroll::-webkit-scrollbar {
  height: 8px;
}

.genres-scroll::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.genres-scroll::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
}

.streaming-section {
  margin: 30px 0;
  padding: 20px;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.streaming-section h2 {
  font-size: 1.8rem;
  margin-bottom: 10px;
  color: #333;
}

.streaming-section h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #444;
}

.streaming-section p {
  color: #666;
  margin-bottom: 20px;
}

.streaming-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (min-width: 768px) {
  .streaming-container {
    flex-direction: row;
    align-items: flex-start;
  }

  .channel-selection {
    flex: 1;
  }

  .player-container {
    flex: 1;
    display: flex;
    justify-content: center;
  }
}

.channel-selection {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.channel-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.channel-button {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  font-size: 1rem;
  color: #333;
}

.channel-button:hover {
  background-color: #e0e0e0;
}

.channel-button.active {
  background-color: #4a86e8;
  color: white;
}

.channel-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
  color: #666;
}

.stream-player-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background-color: #eee;
  border-radius: 8px;
  color: #666;
  font-style: italic;
  width: 100%;
  max-width: 400px;
}

.genre-card {
  flex: 0 0 auto;
  width: 160px;
  padding: 15px;
  border-radius: 10px;
  color: white;
  text-decoration: none;
  text-align: center;
  transition: transform 0.2s ease;
  cursor: pointer;
}

.genre-card:hover {
  transform: translateY(-5px);
}

.genre-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.genre-name {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

/* Test players section */
.test-players-section {
  margin: 30px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.test-players-section h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
}

.test-players-section h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #555;
}

.player-test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin-bottom: 15px;
}

.player-test-button {
  padding: 12px 20px;
  background-color: #3a3a3a;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s, transform 0.2s;
}

.player-test-button:hover {
  background-color: #4a4a4a;
  transform: translateY(-2px);
}

.channel-button {
  background-color: #1e88e5;
  margin-right: 10px;
}

.channel-button:hover {
  background-color: #1976d2;
}

.note {
  font-size: 0.9rem;
  color: #888;
  font-style: italic;
}

/* Channels section */
.channels-section {
  margin: 30px 0;
}

.channels-section h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
}

.channel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.channel-grid button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  height: 120px;
  font-size: 1.1rem;
  background: linear-gradient(45deg, #3a3a3a, #2a2a2a);
  border-radius: 10px;
  color: white;
  border: none;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.channel-grid button:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  background: linear-gradient(45deg, #4a4a4a, #3a3a3a);
}

.error {
  color: #e53935;
  font-weight: 500;
}

/* Play button styles */
button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: background 0.2s;
}

button:hover {
  background: #0056b3;
}