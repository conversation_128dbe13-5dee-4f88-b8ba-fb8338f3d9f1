.selected-genre-page {
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.genre-header {
  padding: 40px 20px;
  color: white;
  position: relative;
}

.back-button {
  color: white;
  text-decoration: none;
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  transition: background-color 0.2s;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

.back-button:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.genre-header-content {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.genre-icon {
  font-size: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.genre-header-content:hover .genre-icon {
  transform: scale(1.05);
}

.genre-header h1 {
  font-size: 3rem;
  margin: 0 0 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.genre-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.genre-channels,
.genre-popular-tracks,
.genre-featured-artists,
.genre-all-tracks {
  margin-bottom: 40px;
}

.genre-channels h2,
.genre-popular-tracks h2,
.genre-featured-artists h2,
.genre-all-tracks h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  transition: color 0.3s ease, border-color 0.3s ease;
}

/* Channels grid styles */
.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.channel-card {
  background-color: var(--bg-secondary);
  border-radius: 10px;
  padding: 15px;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.3s ease;
  overflow: hidden;
  border: 1px solid var(--border-color);
  position: relative;
}

.channel-card.clickable {
  cursor: pointer;
}

.channel-card.clickable:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px var(--shadow);
  background-color: var(--hover-bg);
}

.channel-card.clickable:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

.channel-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px var(--shadow);
  background-color: var(--hover-bg);
}

.channel-image {
  height: 140px;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  position: relative;
}

.channel-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  font-size: 3rem;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.channel-card h3 {
  margin: 0 0 8px;
  font-size: 1.1rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.channel-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 2.8em;
  transition: color 0.3s ease;
}

/* Play overlay styles */
.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.channel-card.clickable:hover .play-overlay {
  opacity: 1;
}

.play-button {
  font-size: 2rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  transform: scale(1);
  transition: transform 0.2s ease;
}

.play-overlay:hover .play-button {
  transform: scale(1.1);
}

/* Channel actions styles */
.channel-actions {
  margin-top: 8px;
  text-align: center;
}

.stream-hint {
  font-size: 0.8rem;
  color: var(--accent-color);
  font-weight: 500;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.channel-card.clickable:hover .stream-hint {
  opacity: 1;
}

.error {
  color: var(--accent-danger);
  font-weight: 500;
  transition: color 0.3s ease;
}

.tracks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.track-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  transition: background-color 0.2s, border-color 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--border-color);
}

.track-item:hover {
  background-color: var(--hover-bg);
}

.track-info h3 {
  margin: 0 0 4px;
  font-size: 1.1rem;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.track-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.track-duration {
  color: var(--text-muted);
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.artists-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
}

.artist-card {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s, background-color 0.3s ease, box-shadow 0.2s;
  border: 1px solid var(--border-color);
}

.artist-card:hover {
  transform: translateY(-5px);
  background-color: var(--hover-bg);
  box-shadow: 0 5px 15px var(--shadow);
}

.artist-image {
  font-size: 2.5rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.artist-card h3 {
  margin: 0 0 5px;
  font-size: 1.1rem;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.artist-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.genre-not-found {
  text-align: center;
  padding: 60px 20px;
}

.genre-not-found h2 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

.genre-not-found p {
  margin-bottom: 20px;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}