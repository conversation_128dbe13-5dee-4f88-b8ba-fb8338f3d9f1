/* Theme System - CSS Custom Properties */

/* Root theme variables */
:root {
  /* Light Theme Colors */
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f8f9fa;
  --bg-tertiary-light: #e9ecef;
  --text-primary-light: #212529;
  --text-secondary-light: #6c757d;
  --text-muted-light: #adb5bd;
  --border-light: #dee2e6;
  --border-hover-light: #ced4da;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --overlay-light: rgba(0, 0, 0, 0.5);
  --hover-bg-light: rgba(0, 0, 0, 0.05);
  --active-bg-light: rgba(0, 0, 0, 0.1);

  /* Dark Theme Colors */
  --bg-primary-dark: #1e1e1e;
  --bg-secondary-dark: #2a2a2a;
  --bg-tertiary-dark: #3a3a3a;
  --text-primary-dark: #ffffff;
  --text-secondary-dark: #b8b8b8;
  --text-muted-dark: #888888;
  --border-dark: #404040;
  --border-hover-dark: #505050;
  --shadow-dark: rgba(0, 0, 0, 0.25);
  --overlay-dark: rgba(0, 0, 0, 0.6);
  --hover-bg-dark: rgba(255, 255, 255, 0.08);
  --active-bg-dark: rgba(255, 255, 255, 0.12);

  /* Accent Colors (consistent across themes) */
  --accent-primary: #ff0000;
  --accent-primary-hover: #e60000;
  --accent-secondary: #007bff;
  --accent-success: #28a745;
  --accent-warning: #ffc107;
  --accent-danger: #dc3545;

  /* Scrollbar Colors */
  --scrollbar-thumb-light: #c1c1c1;
  --scrollbar-thumb-hover-light: #a8a8a8;
  --scrollbar-track-light: #f1f1f1;
  --scrollbar-thumb-dark: #606060;
  --scrollbar-thumb-hover-dark: #808080;
  --scrollbar-track-dark: transparent;
}

/* Default theme (system preference detection) */
:root {
  --bg-primary: var(--bg-primary-dark);
  --bg-secondary: var(--bg-secondary-dark);
  --bg-tertiary: var(--bg-tertiary-dark);
  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --text-muted: var(--text-muted-dark);
  --border-color: var(--border-dark);
  --border-hover: var(--border-hover-dark);
  --shadow: var(--shadow-dark);
  --overlay: var(--overlay-dark);
  --hover-bg: var(--hover-bg-dark);
  --active-bg: var(--active-bg-dark);
  --scrollbar-thumb: var(--scrollbar-thumb-dark);
  --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-dark);
  --scrollbar-track: var(--scrollbar-track-dark);
}

/* Light theme class */
.theme-light {
  --bg-primary: var(--bg-primary-light);
  --bg-secondary: var(--bg-secondary-light);
  --bg-tertiary: var(--bg-tertiary-light);
  --text-primary: var(--text-primary-light);
  --text-secondary: var(--text-secondary-light);
  --text-muted: var(--text-muted-light);
  --border-color: var(--border-light);
  --border-hover: var(--border-hover-light);
  --shadow: var(--shadow-light);
  --overlay: var(--overlay-light);
  --hover-bg: var(--hover-bg-light);
  --active-bg: var(--active-bg-light);
  --scrollbar-thumb: var(--scrollbar-thumb-light);
  --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-light);
  --scrollbar-track: var(--scrollbar-track-light);
}

/* Dark theme class */
.theme-dark {
  --bg-primary: var(--bg-primary-dark);
  --bg-secondary: var(--bg-secondary-dark);
  --bg-tertiary: var(--bg-tertiary-dark);
  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --text-muted: var(--text-muted-dark);
  --border-color: var(--border-dark);
  --border-hover: var(--border-hover-dark);
  --shadow: var(--shadow-dark);
  --overlay: var(--overlay-dark);
  --hover-bg: var(--hover-bg-dark);
  --active-bg: var(--active-bg-dark);
  --scrollbar-thumb: var(--scrollbar-thumb-dark);
  --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-dark);
  --scrollbar-track: var(--scrollbar-track-dark);
}

/* System preference detection */
@media (prefers-color-scheme: light) {
  :root:not(.theme-dark):not(.theme-light) {
    --bg-primary: var(--bg-primary-light);
    --bg-secondary: var(--bg-secondary-light);
    --bg-tertiary: var(--bg-tertiary-light);
    --text-primary: var(--text-primary-light);
    --text-secondary: var(--text-secondary-light);
    --text-muted: var(--text-muted-light);
    --border-color: var(--border-light);
    --border-hover: var(--border-hover-light);
    --shadow: var(--shadow-light);
    --overlay: var(--overlay-light);
    --hover-bg: var(--hover-bg-light);
    --active-bg: var(--active-bg-light);
    --scrollbar-thumb: var(--scrollbar-thumb-light);
    --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-light);
    --scrollbar-track: var(--scrollbar-track-light);
  }
}

/* Smooth theme transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Disable transitions during theme change to prevent flash */
.theme-transitioning * {
  transition: none !important;
}

/* Theme toggle button styles */
.theme-toggle {
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 48px;
  background: none;
  border: none;
  color: var(--text-secondary);
  gap: 24px;
  cursor: pointer;
  width: 100%;
  text-align: left;
  transition: all 0.3s ease;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
}

.theme-toggle:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.theme-toggle svg {
  width: 24px;
  height: 24px;
  color: inherit;
  flex-shrink: 0;
}

.theme-toggle span {
  transition: opacity 0.3s ease;
  white-space: nowrap;
}

/* Collapsed sidebar theme toggle */
.sidebar.collapsed .theme-toggle {
  padding: 0 20px;
  justify-content: center;
  gap: 0;
  position: relative;
}

.sidebar.collapsed .theme-toggle span {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Tooltip for collapsed theme toggle */
.sidebar.collapsed .theme-toggle::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  margin-left: 8px;
  z-index: 1000;
  box-shadow: 0 2px 8px var(--shadow);
}

.sidebar.collapsed .theme-toggle:hover::after {
  opacity: 1;
}

/* Theme section divider */
.theme-section {
  padding: 8px 0;
}

.theme-section::before {
  content: '';
  height: 1px;
  background: var(--border-color);
  margin: 8px 0;
  display: block;
}
