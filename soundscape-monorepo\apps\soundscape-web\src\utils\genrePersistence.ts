/**
 * Utility functions for persisting genre selection state
 */

const SELECTED_GENRE_KEY = 'soundscape_selected_genre';

export interface PersistedGenreState {
  genreId: number;
  timestamp: number;
  genreName?: string;
}

/**
 * Save the selected genre to localStorage
 */
export const saveSelectedGenre = (genreId: number, genreName?: string): void => {
  try {
    const state: PersistedGenreState = {
      genreId,
      timestamp: Date.now(),
      genreName
    };
    localStorage.setItem(SELECTED_GENRE_KEY, JSON.stringify(state));
    console.log(`🎵 Saved selected genre ${genreId} to localStorage`);
  } catch (error) {
    console.warn('Failed to save selected genre to localStorage:', error);
  }
};

/**
 * Load the selected genre from localStorage
 * Returns null if not found or expired (older than 1 hour)
 */
export const loadSelectedGenre = (): PersistedGenreState | null => {
  try {
    const stored = localStorage.getItem(SELECTED_GENRE_KEY);
    if (!stored) {
      return null;
    }

    const state: PersistedGenreState = JSON.parse(stored);
    
    // Check if the stored state is too old (1 hour)
    const maxAge = 60 * 60 * 1000; // 1 hour in milliseconds
    if (Date.now() - state.timestamp > maxAge) {
      console.log('🎵 Stored genre selection expired, clearing');
      clearSelectedGenre();
      return null;
    }

    console.log(`🎵 Loaded selected genre ${state.genreId} from localStorage`);
    return state;
  } catch (error) {
    console.warn('Failed to load selected genre from localStorage:', error);
    return null;
  }
};

/**
 * Clear the selected genre from localStorage
 */
export const clearSelectedGenre = (): void => {
  try {
    localStorage.removeItem(SELECTED_GENRE_KEY);
    console.log('🎵 Cleared selected genre from localStorage');
  } catch (error) {
    console.warn('Failed to clear selected genre from localStorage:', error);
  }
};

/**
 * Check if a genre is currently persisted
 */
export const isGenrePersisted = (genreId: number): boolean => {
  const stored = loadSelectedGenre();
  return stored?.genreId === genreId;
};
