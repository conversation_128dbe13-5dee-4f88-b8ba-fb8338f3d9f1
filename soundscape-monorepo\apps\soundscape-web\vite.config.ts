/// <reference types='vitest' />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';
import { nxCopyAssetsPlugin } from '@nx/vite/plugins/nx-copy-assets.plugin';

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/soundscape-web',
  server: {
    port: 4200,
    host: 'localhost',
    proxy: {
      '/api/genres': {
        target: process.env.VITE_GENRES_API_URL || 'http://localhost:4001',
        changeOrigin: true,
        secure: false,
      },
      '/api/genrechannels': {
        target: process.env.VITE_GENRES_API_URL || 'http://localhost:4001',
        changeOrigin: true,
        secure: false,
      },
      '/api/stream': {
        target: process.env.VITE_STREAMING_API_URL || 'http://localhost:4004',
        changeOrigin: true,
        secure: false,
      },
      '/api/channels': {
        target: process.env.VITE_STREAMING_API_URL || 'http://localhost:4004',
        changeOrigin: true,
        secure: false,
      },
      '/api/user': {
        target: process.env.VITE_USER_API_URL || 'http://localhost:4002',
        changeOrigin: true,
        secure: false,
      },
      '/api/auth': {
        target: process.env.VITE_AUTH_API_URL || 'http://localhost:4000',
        changeOrigin: true,
        secure: false,
      },
      '/api/usage': {
        target: process.env.VITE_USAGE_API_URL || 'http://localhost:4005',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  preview: {
    port: 4300,
    host: 'localhost',
  },
  plugins: [react(), nxViteTsPaths(), nxCopyAssetsPlugin(['*.md'])],
  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },
  build: {
    outDir: './build/soundscape-web',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  test: {
    watch: false,
    globals: true,
    environment: 'jsdom',
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    reporters: ['default'],
    coverage: {
      reportsDirectory: '../../coverage/apps/soundscape-web',
      provider: 'v8',
    },
  },
});
