{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "nxCloudId": "67ba2970912c9e2133c94dc2", "plugins": [{"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}}], "targetDefaults": {"e2e-ci--**/*": {"dependsOn": ["^build"]}}, "generators": {"@nx/react": {"application": {"babel": true, "style": "css", "linter": "eslint", "bundler": "vite"}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint", "unitTestRunner": "jest"}}}, "release": {"version": {"preVersionCommand": "npx nx run-many -t build"}}}