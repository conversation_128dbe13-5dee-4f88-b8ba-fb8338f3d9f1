{"name": "@soundscape-monorepo/source", "version": "0.0.0", "license": "MIT", "scripts": {"install-all": "npm install && nx run-many --target=install --all", "install-deps": "npm install && cd apps/soundscape-web && npm install && cd ../../soundscape-monorepo-redux && npm install", "start": "nx serve soundscape-web", "dev": "nx serve soundscape-web", "build": "nx build soundscape-web", "lint": "nx lint soundscape-web", "test": "nx test soundscape-web"}, "private": true, "dependencies": {"@nrwl/react": "^19.8.4", "@types/redux-logger": "^3.0.13", "react": "19.0.0", "react-dom": "19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "redux-logger": "^3.0.6"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/js": "^9.8.0", "@nx/devkit": "20.4.6", "@nx/eslint": "20.4.6", "@nx/eslint-plugin": "20.4.6", "@nx/jest": "20.4.6", "@nx/js": "20.4.6", "@nx/playwright": "20.4.6", "@nx/react": "20.4.6", "@nx/vite": "20.4.6", "@nx/web": "20.4.6", "@nx/workspace": "20.4.6", "@playwright/test": "^1.36.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/jest": "^29.5.12", "@types/node": "18.16.9", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.3.1", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "~22.1.0", "nx": "20.4.6", "prettier": "^2.6.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "verdaccio": "^5.0.4", "vite": "^5.0.0", "vite-plugin-dts": "~3.8.1", "vitest": "^1.3.1"}, "nx": {"includedScripts": []}}