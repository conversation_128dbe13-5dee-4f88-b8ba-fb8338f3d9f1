{"name": "soundscape-monorepo-redux", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "soundscape-monorepo-redux/src", "projectType": "library", "tags": [], "targets": {"nx-release-publish": {"options": {"packageRoot": "dist/{projectRoot}"}}}, "release": {"version": {"generatorOptions": {"packageRoot": "dist/{projectRoot}", "currentVersionResolver": "git-tag", "fallbackCurrentVersionResolver": "disk"}}}}