// soundscape-monorepo-redux/src/actions.ts

// Action types as constants to avoid typos
export const INCREMENT_COUNTER = 'INCREMENT_COUNTER';
export const DECREMENT_COUNTER = 'DECREMENT_COUNTER';
export const UPDATE_USER_NAME = 'UPDATE_USER_NAME';
export const UPDATE_USER_AGE = 'UPDATE_USER_AGE';

// Music Player action types
export const PLAY_SONG = 'PLAY_SONG';
export const PAUSE_SONG = 'PAUSE_SONG';
export const RESUME_SONG = 'RESUME_SONG';
export const UPDATE_PROGRESS = 'UPDATE_PROGRESS';

// Genre action types
export const FETCH_GENRES_REQUEST = 'FETCH_GENRES_REQUEST';
export const FETCH_GENRES_SUCCESS = 'FETCH_GENRES_SUCCESS';
export const FETCH_GENRES_FAILURE = 'FETCH_GENRES_FAILURE';

// Counter action types
interface IncrementCounterAction {
  type: typeof INCREMENT_COUNTER;
}

interface DecrementCounterAction {
  type: typeof DECREMENT_COUNTER;
}

// User action types
interface UpdateUserNameAction {
  type: typeof UPDATE_USER_NAME;
  payload: string;
}

interface UpdateUserAgeAction {
  type: typeof UPDATE_USER_AGE;
  payload: number;
}

// Music Player action types
export interface Song {
  id: string;
  title: string;
  artist: string;
  url: string;
  albumArt?: string;
}

interface PlaySongAction {
  type: typeof PLAY_SONG;
  payload: Song;
}

interface PauseSongAction {
  type: typeof PAUSE_SONG;
}

interface ResumeSongAction {
  type: typeof RESUME_SONG;
}

interface UpdateProgressAction {
  type: typeof UPDATE_PROGRESS;
  payload: number;
}

// Genre action types
export interface Genre {
  id: number;
  name: string;
  description?: string;
  image?: string;
  url?: string;
  path?: string;
  order?: number;
  color?: string; // Optional - will be generated if not provided
  icon?: string;  // Optional - will be generated if not provided
  [key: string]: any;
}

interface FetchGenresRequestAction {
  type: typeof FETCH_GENRES_REQUEST;
}

interface FetchGenresSuccessAction {
  type: typeof FETCH_GENRES_SUCCESS;
  payload: Genre[];
}

interface FetchGenresFailureAction {
  type: typeof FETCH_GENRES_FAILURE;
  payload: string;
}

// Union type for all actions
export type CounterAction = IncrementCounterAction | DecrementCounterAction;
export type UserAction = UpdateUserNameAction | UpdateUserAgeAction;
export type MusicPlayerAction = PlaySongAction | PauseSongAction | ResumeSongAction | UpdateProgressAction;
export type GenreAction = FetchGenresRequestAction | FetchGenresSuccessAction | FetchGenresFailureAction;
export type AppAction = CounterAction | UserAction | MusicPlayerAction | GenreAction;

// Action creators
export const incrementCounter = (): IncrementCounterAction => ({
  type: INCREMENT_COUNTER,
});

export const decrementCounter = (): DecrementCounterAction => ({
  type: DECREMENT_COUNTER,
});

export const updateUserName = (name: string): UpdateUserNameAction => ({
  type: UPDATE_USER_NAME,
  payload: name,
});

export const updateUserAge = (age: number): UpdateUserAgeAction => ({
  type: UPDATE_USER_AGE,
  payload: age,
});

// Music Player action creators
export const playSong = (song: Song): PlaySongAction => ({
  type: PLAY_SONG,
  payload: song,
});

export const pauseSong = (): PauseSongAction => ({
  type: PAUSE_SONG,
});

export const resumeSong = (): ResumeSongAction => ({
  type: RESUME_SONG,
});

export const updateProgress = (progress: number): UpdateProgressAction => ({
  type: UPDATE_PROGRESS,
  payload: progress,
});

// Genre action creators
export const fetchGenresRequest = (): FetchGenresRequestAction => ({
  type: FETCH_GENRES_REQUEST,
});

export const fetchGenresSuccess = (genres: Genre[]): FetchGenresSuccessAction => ({
  type: FETCH_GENRES_SUCCESS,
  payload: genres,
});

export const fetchGenresFailure = (error: string): FetchGenresFailureAction => ({
  type: FETCH_GENRES_FAILURE,
  payload: error,
});