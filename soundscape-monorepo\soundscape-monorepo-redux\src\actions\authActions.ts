// Action Types
export const LOGIN_REQUEST = 'auth/LOGIN_REQUEST';
export const LOGIN_SUCCESS = 'auth/LOGIN_SUCCESS';
export const LOGIN_FAILURE = 'auth/LOGIN_FAILURE';
export const SIGNUP_REQUEST = 'auth/SIGNUP_REQUEST';
export const SIGNUP_SUCCESS = 'auth/SIGNUP_SUCCESS';
export const SIGNUP_FAILURE = 'auth/SIGNUP_FAILURE';
export const LOGOUT = 'auth/LOGOUT';

// User interfaces
export interface User {
  id: string;
  name: string;
  email: string;
}

// Action Interfaces
interface LoginRequestAction {
  type: typeof LOGIN_REQUEST;
}

interface LoginSuccessAction {
  type: typeof LOGIN_SUCCESS;
  payload: User;
}

interface LoginFailureAction {
  type: typeof LOGIN_FAILURE;
  payload: string;
}

interface SignupRequestAction {
  type: typeof SIGNUP_REQUEST;
}

interface SignupSuccessAction {
  type: typeof SIGNUP_SUCCESS;
  payload: User;
}

interface SignupFailureAction {
  type: typeof SIGNUP_FAILURE;
  payload: string;
}

interface LogoutAction {
  type: typeof LOGOUT;
}

export type AuthAction =
  | LoginRequestAction
  | LoginSuccessAction
  | LoginFailureAction
  | SignupRequestAction
  | SignupSuccessAction
  | SignupFailureAction
  | LogoutAction;

// Action Creators
export const loginRequest = (): AuthAction => ({
  type: LOGIN_REQUEST
});

export const loginSuccess = (user: User): AuthAction => ({
  type: LOGIN_SUCCESS,
  payload: user
});

export const loginFailure = (error: string): AuthAction => ({
  type: LOGIN_FAILURE,
  payload: error
});

export const signupRequest = (): AuthAction => ({
  type: SIGNUP_REQUEST
});

export const signupSuccess = (user: User): AuthAction => ({
  type: SIGNUP_SUCCESS,
  payload: user
});

export const signupFailure = (error: string): AuthAction => ({
  type: SIGNUP_FAILURE,
  payload: error
});

export const logout = (): AuthAction => ({
  type: LOGOUT
}); 