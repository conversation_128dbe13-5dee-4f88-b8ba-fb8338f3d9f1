// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4004';
export const GENRE_API_URL = import.meta.env.VITE_GENRE_API_URL || 'http://localhost:4001';
export const STREAMING_API_URL = import.meta.env.VITE_STREAMING_API_URL || 'http://localhost:4004';

// API Endpoints
export const API_ENDPOINTS = {
  // Genre API
  GENRES: `${GENRE_API_URL}/api/genres`,
  GENRE_CHANNELS: `${GENRE_API_URL}/api/genrechannels`,
  
  // Streaming API
  CHANNELS: `${STREAMING_API_URL}/api/channels`,
  STREAM: `${STREAMING_API_URL}/api/stream`,
  
  // Auth (if needed)
  AUTH: `${API_BASE_URL}/api/auth`
};

export default {
  API_BASE_URL,
  GENRE_API_URL,
  STREAMING_API_URL,
  API_ENDPOINTS
};
