// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4004';
export const GENRE_API_URL = import.meta.env.VITE_GENRE_API_URL || 'http://localhost:4001';
export const STREAMING_API_URL = import.meta.env.VITE_STREAMING_API_URL || 'http://localhost:4004';
export const USER_API_URL = import.meta.env.VITE_USER_API_URL || 'http://localhost:4002';
export const AUTH_API_URL = import.meta.env.VITE_AUTH_API_URL || 'http://localhost:4000';

// API Endpoints
export const API_ENDPOINTS = {
  // Genre API
  GENRES: `${GENRE_API_URL}/api/genres`,
  GENRE_CHANNELS: `${GENRE_API_URL}/api/genrechannels`,

  // Streaming API
  CHANNELS: `${STREAMING_API_URL}/api/channels`,
  STREAM: `${STREAMING_API_URL}/api/stream`,

  // User API
  USER_PROFILE: `${USER_API_URL}/api/user/profile`,
  USER_EMAIL: `${USER_API_URL}/api/user/email`,
  USER_PASSWORD: `${USER_API_URL}/api/user/password`,
  USER_ACCOUNT: `${USER_API_URL}/api/user/account`,
  USER_PREFERENCES: `${USER_API_URL}/api/user/preferences`,

  // Auth API
  AUTH_LOGIN: `${AUTH_API_URL}/api/auth/login`,
  AUTH_CALLBACK: `${AUTH_API_URL}/api/auth/callback`,

  // User Auth endpoints
  USER_LOGIN: `${USER_API_URL}/api/user/login`,
  USER_REGISTER: `${USER_API_URL}/api/user/register`,
  USER_LOGOUT: `${USER_API_URL}/api/user/logout`
};

export default {
  API_BASE_URL,
  GENRE_API_URL,
  STREAMING_API_URL,
  USER_API_URL,
  AUTH_API_URL,
  API_ENDPOINTS
};
