// soundscape-monorepo-redux/src/reducers.ts

import { combineReducers } from 'redux';
import userReducer from './reducers/userReducer';
import counterReducer from './reducers/counterReducer';
import musicPlayerReducer from './reducers/musicPlayerReducer';
import { genreListReducer } from './reducers/genresReducer';
import { authReducer } from './reducers/authReducer';
import playerReducer from './slices/playerSlice';
import genreReducer from './slices/genreSlice';

export const rootReducer = combineReducers({
  user: userReducer,
  counter: counterReducer,
  musicPlayer: musicPlayerReducer,
  genres: genreReducer,
  auth: authReducer,
  player: playerReducer
});