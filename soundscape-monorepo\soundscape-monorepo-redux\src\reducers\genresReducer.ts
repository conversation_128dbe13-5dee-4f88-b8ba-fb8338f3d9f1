import {
  FETCH_GENRES_REQUEST,
  FETCH_GENRES_SUCCESS,
  FETCH_GENRES_FAILURE,
  Genre,
  GenreAction
} from '../actions';

export interface GenreListState {
  items: Genre[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null; // Timestamp of last fetch for caching
}

const initialState: GenreListState = {
  items: [],
  loading: false,
  error: null,
  lastFetched: null
};

// Cache expiration time (5 minutes in milliseconds)
const CACHE_EXPIRATION = 5 * 60 * 1000;

export const genreListReducer = (
  state = initialState,
  action: GenreAction
): GenreListState => {
  switch (action.type) {
    case FETCH_GENRES_REQUEST:
      return {
        ...state,
        loading: true,
        error: null
      };
    case FETCH_GENRES_SUCCESS:
      return {
        ...state,
        loading: false,
        items: action.payload,
        error: null,
        lastFetched: Date.now() // Set the timestamp when successfully fetched
      };
    case FETCH_GENRES_FAILURE:
      return {
        ...state,
        loading: false,
        error: action.payload
      };
    default:
      return state;
  }
};

// Selector to check if the cache is valid
export const shouldFetchGenres = (state: GenreListState | undefined): boolean => {
  // If state is undefined or there's no cached data or the cache is expired, we should fetch
  if (!state || !state.lastFetched) return true;
  
  // Also fetch if there are no items
  if (state.items.length === 0) return true;
  
  const now = Date.now();
  return now - state.lastFetched > CACHE_EXPIRATION;
}; 