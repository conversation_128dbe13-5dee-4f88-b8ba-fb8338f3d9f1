import { 
  <PERSON><PERSON>layerA<PERSON>, 
  Song, 
  PLAY_SONG, 
  PAUSE_SONG, 
  RESUME_SONG, 
  UPDATE_PROGRESS 
} from '../actions';

export interface MusicPlayerState {
  currentSong: Song | null;
  isPlaying: boolean;
  progress: number;
}

const initialMusicPlayerState: MusicPlayerState = {
  currentSong: null,
  isPlaying: false,
  progress: 0,
};

const musicPlayerReducer = (
  state = initialMusicPlayerState, 
  action: MusicPlayerAction
): MusicPlayerState => {
  switch (action.type) {
    case PLAY_SONG:
      return { 
        ...state, 
        currentSong: action.payload, 
        isPlaying: true,
        progress: 0 
      };
    case PAUSE_SONG:
      return { 
        ...state, 
        isPlaying: false 
      };
    case RESUME_SONG:
      return { 
        ...state, 
        isPlaying: true 
      };
    case UPDATE_PROGRESS:
      return { 
        ...state, 
        progress: action.payload 
      };
    default:
      return state;
  }
};

export default musicPlayerReducer; 