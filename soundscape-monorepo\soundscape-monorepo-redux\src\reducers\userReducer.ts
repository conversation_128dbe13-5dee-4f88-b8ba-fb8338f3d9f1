// soundscape-monorepo-redux/src/reducers/userReducer.ts

import { UserAction, UPDATE_USER_NAME, UPDATE_USER_AGE } from '../actions';

type UserState = {
  name: string;
  age: number;
};

const initialUserState: UserState = {
  name: '<PERSON>',
  age: 30,
};

const userReducer = (state = initialUserState, action: UserAction): UserState => {
  switch (action.type) {
    case UPDATE_USER_NAME:
      return { ...state, name: action.payload };
    case UPDATE_USER_AGE:
      return { ...state, age: action.payload };
    default:
      return state;
  }
};

export default userReducer;