import { User } from '../actions/authActions';
import { API_ENDPOINTS } from '../config';

// Helper function to get API URL from environment variables
const getApiUrl = (endpoint: string): string => {
  // Use the environment variable if available, otherwise use the proxy
  return import.meta.env.VITE_USER_API_URL ?
    endpoint :
    endpoint.replace('http://localhost:4002', '');
};

// Simulated delay for development
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Real login API call
export const loginWithEmailAndPassword = async (email: string, password: string): Promise<User> => {
  await delay(300); // Small delay for UX

  try {
    const response = await fetch('/api/user/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error_description || 'Invalid email or password');
    }

    const data = await response.json();

    // Store auth token in localStorage
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('authToken', data.token);

    return {
      id: data.user.id.toString(),
      name: data.user.name,
      email: data.user.email
    };
  } catch (error) {
    console.error('Login error:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Login failed');
  }
};

// Real signup API call
export const signupWithEmailAndPassword = async (
  name: string,
  email: string,
  password: string
): Promise<User> => {
  await delay(500); // Small delay for UX

  try {
    const response = await fetch('/api/user/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name, email, password })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error_description || 'Failed to create account');
    }

    const data = await response.json();

    // Store auth token in localStorage
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('authToken', data.token);

    return {
      id: data.user.id.toString(),
      name: data.user.name,
      email: data.user.email
    };
  } catch (error) {
    console.error('Signup error:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to create account');
  }
};

// Real logout
export const logout = async (): Promise<void> => {
  await delay(300); // Small delay for UX
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('authToken');
};