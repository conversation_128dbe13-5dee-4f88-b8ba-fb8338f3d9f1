import { User } from '../actions/authActions';
import { API_ENDPOINTS } from '../config';

// Helper function to get API URL from environment variables
const getApiUrl = (endpoint: string): string => {
  // Use the environment variable if available, otherwise use the proxy
  return import.meta.env.VITE_USER_API_URL ?
    endpoint :
    endpoint.replace('http://localhost:4002', '');
};

// Simulated delay for development
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Real login API call
export const loginWithEmailAndPassword = async (email: string, password: string): Promise<User> => {
  await delay(300); // Small delay for UX

  try {
    const response = await fetch('/api/user/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      // Use the API's error_description if available, otherwise use a generic message
      const errorMessage = errorData.error_description || 'Invalid email or password';
      throw new Error(errorMessage);
    }

    const data = await response.json();

    // Store auth token in localStorage
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('authToken', data.token);

    return {
      id: data.user.id.toString(),
      name: data.user.name,
      email: data.user.email
    };
  } catch (error) {
    console.error('Login error:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Login failed');
  }
};

// Real signup API call
export const signupWithEmailAndPassword = async (
  name: string,
  email: string,
  password: string
): Promise<User> => {
  await delay(500); // Small delay for UX

  try {
    const response = await fetch('/api/user/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name, email, password })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      // Use the API's error_description if available, otherwise use a generic message
      const errorMessage = errorData.error_description || 'Failed to create account';
      throw new Error(errorMessage);
    }

    const data = await response.json();

    // Store auth token in localStorage
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('authToken', data.token);

    return {
      id: data.user.id.toString(),
      name: data.user.name,
      email: data.user.email
    };
  } catch (error) {
    console.error('Signup error:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to create account');
  }
};

// Real logout
export const logout = async (): Promise<void> => {
  await delay(300); // Small delay for UX

  try {
    // Call the logout API endpoint
    const response = await fetch('/api/user/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken') || ''}`
      }
    });

    // Even if the API call fails, we still want to clear local storage
    if (!response.ok) {
      console.warn('Logout API call failed, but clearing local storage anyway');
    }
  } catch (error) {
    console.warn('Logout API call failed, but clearing local storage anyway:', error);
  } finally {
    // Always clear local storage regardless of API response
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('authToken');
  }
};