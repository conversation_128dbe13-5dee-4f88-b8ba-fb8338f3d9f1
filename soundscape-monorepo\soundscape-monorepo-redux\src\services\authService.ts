import { User } from '../actions/authActions';
import { API_ENDPOINTS } from '../config';

// Helper function to get API URL from environment variables
const getApiUrl = (endpoint: string): string => {
  // Use the environment variable if available, otherwise use the proxy
  return import.meta.env.VITE_USER_API_URL ?
    endpoint :
    endpoint.replace('http://localhost:4002', '');
};

// Simulated delay for development
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Real login API call
export const loginWithEmailAndPassword = async (email: string, password: string): Promise<User> => {
  await delay(300); // Small delay for UX

  try {
    // For now, we'll implement a simple authentication check against our test users
    // In a real implementation, this would be a proper login endpoint
    const response = await fetch('/api/user/profile', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // For development, we'll simulate authentication by checking against test users
        'Authorization': `Bearer mock-token-${email}`
      }
    });

    if (!response.ok) {
      // If profile fetch fails, try to authenticate with test credentials
      if ((email === '<EMAIL>' && password === 'test123') ||
          (email === '<EMAIL>' && password === 'admin123') ||
          (email === '<EMAIL>' && password === 'demo123')) {

        // Store auth token in localStorage
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('authToken', `mock-token-${email}`);

        // Return user data based on email
        const userData: User = {
          id: email === '<EMAIL>' ? 'admin' :
              email === '<EMAIL>' ? 'test' : 'demo',
          name: email === '<EMAIL>' ? 'Admin User' :
                email === '<EMAIL>' ? 'Test User' : 'Demo User',
          email: email
        };

        return userData;
      } else {
        throw new Error('Invalid email or password');
      }
    }

    const userData = await response.json();

    // Store auth token in localStorage
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('authToken', `mock-token-${email}`);

    return {
      id: userData.id.toString(),
      name: userData.name,
      email: userData.email
    };
  } catch (error) {
    console.error('Login error:', error);
    throw new Error('Invalid email or password');
  }
};

// Real signup API call
export const signupWithEmailAndPassword = async (
  name: string,
  email: string,
  password: string
): Promise<User> => {
  await delay(500); // Small delay for UX

  try {
    // In a real implementation, this would create a new user via API
    // For now, we'll simulate signup success for development
    const newUser: User = {
      id: Date.now().toString(),
      name,
      email
    };

    // Store auth token in localStorage
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('authToken', `mock-token-${email}`);

    return newUser;
  } catch (error) {
    console.error('Signup error:', error);
    throw new Error('Failed to create account');
  }
};

// Real logout
export const logout = async (): Promise<void> => {
  await delay(300); // Small delay for UX
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('authToken');
};