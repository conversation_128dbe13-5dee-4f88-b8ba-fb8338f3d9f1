import { User } from '../actions/authActions';

// Mock user data
const mockUsers: User[] = [
  {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>'
  }
];

// Simulated delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock login API call
export const loginWithEmailAndPassword = async (email: string, password: string): Promise<User> => {
  await delay(800); // Simulate network delay

  // In a real app, this would be an API call
  const user = mockUsers.find(u => u.email === email);
  
  if (!user) {
    throw new Error('Invalid email or password');
  }

  // Store auth token in localStorage (in a real app, this would be a JWT)
  localStorage.setItem('isAuthenticated', 'true');
  
  return user;
};

// Mock signup API call
export const signupWithEmailAndPassword = async (
  name: string,
  email: string,
  password: string
): Promise<User> => {
  await delay(800); // Simulate network delay

  // Check if user already exists
  if (mockUsers.some(u => u.email === email)) {
    throw new Error('Email already exists');
  }

  // Create new user
  const newUser: User = {
    id: String(mockUsers.length + 1),
    name,
    email
  };

  // In a real app, this would be an API call
  mockUsers.push(newUser);

  // Store auth token in localStorage (in a real app, this would be a JWT)
  localStorage.setItem('isAuthenticated', 'true');
  
  return newUser;
};

// Mock logout
export const logout = async (): Promise<void> => {
  await delay(300); // Simulate network delay
  localStorage.removeItem('isAuthenticated');
}; 