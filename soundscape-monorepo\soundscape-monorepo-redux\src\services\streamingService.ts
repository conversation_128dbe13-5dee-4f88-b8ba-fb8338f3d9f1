import { STREAMING_API_URL } from '../config';

export interface StreamingTrack {
  id: number;
  title: string;
  url: string;
  weight: number;
  albumId: number;
  albumTitle: string;
  albumImage: string;
  artists: Array<{
    id: number;
    name: string;
    role: string;
  }>;
  duration: number;
}

export interface ChannelTracksResponse {
  channelInfo: {
    id: number;
    name: string;
    description: string;
    image: string;
    isPublic: boolean;
    isActive: boolean;
  };
  tracks: StreamingTrack[];
}

export interface TrackInfo {
  trackIndex: number;
  totalTracks: number;
  track: {
    id: number;
    title: string;
    type: string;
    duration: number;
  };
}

export interface SkipTrackResponse {
  success: boolean;
  nextTrackIndex: number;
  totalTracks: number;
}

class StreamingService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = STREAMING_API_URL || 'http://localhost:4004';
  }

  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('authToken');
    const headers: HeadersInit = {
      'Content-Type': 'application/json'
    };

    // Only add authorization header if we have a valid token
    if (token && token !== 'null' && token !== 'undefined') {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Get tracks for a specific channel
   */
  async getChannelTracks(channelId: string | number): Promise<ChannelTracksResponse> {
    const response = await fetch(`${this.baseUrl}/api/channels/${channelId}/tracks`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch channel tracks: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get stream URL for a channel at a specific track index
   */
  getStreamUrl(channelId: string | number, trackIndex: number = 0): string {
    const token = localStorage.getItem('authToken');
    const params = new URLSearchParams({
      trackIndex: trackIndex.toString()
    });

    // Only add token if we have a valid one
    if (token && token !== 'null' && token !== 'undefined') {
      params.append('token', token);
    }

    return `${this.baseUrl}/api/stream/${channelId}?${params}`;
  }

  /**
   * Get current track information
   */
  async getTrackInfo(channelId: string | number, trackIndex: number = 0): Promise<TrackInfo> {
    const response = await fetch(`${this.baseUrl}/api/stream/${channelId}/info?trackIndex=${trackIndex}`, {
      method: 'GET',
      headers: this.getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch track info: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Skip to next track
   */
  async skipTrack(channelId: string | number, currentTrackIndex: number): Promise<SkipTrackResponse> {
    const response = await fetch(`${this.baseUrl}/api/stream/${channelId}/skip`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ currentTrackIndex })
    });

    if (!response.ok) {
      throw new Error(`Failed to skip track: ${response.statusText}`);
    }

    return response.json();
  }
}

export const streamingService = new StreamingService();
export default streamingService;
