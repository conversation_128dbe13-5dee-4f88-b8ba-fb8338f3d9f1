import { API_ENDPOINTS } from '../config';

// User interface matching the database model
export interface UserProfile {
  id: string;
  email: string;
  name: string;
  gender?: string;
  phone?: string;
  description?: string;
  image?: string;
  is_public?: boolean;
  url?: string;
  is_active: boolean;
  is_admin: boolean;
  created_date: string;
  updated_date?: string;
  last_login?: string;
}

// Helper function to get auth headers
const getAuthHeaders = (): HeadersInit => {
  const token = localStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function to get API URL from environment variables
const getApiUrl = (endpoint: string): string => {
  // Use the environment variable if available, otherwise use the proxy
  return import.meta.env.VITE_USER_API_URL ?
    endpoint :
    endpoint.replace('http://localhost:4002', '');
};

// Get user profile
export const getUserProfile = async (): Promise<UserProfile> => {
  const url = getApiUrl(API_ENDPOINTS.USER_PROFILE);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch user profile: ${response.statusText}`);
  }

  return response.json();
};

// Update user profile
export const updateUserProfile = async (updates: Partial<UserProfile>): Promise<UserProfile> => {
  const url = getApiUrl(API_ENDPOINTS.USER_PROFILE);
  
  const response = await fetch(url, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(updates)
  });

  if (!response.ok) {
    throw new Error(`Failed to update user profile: ${response.statusText}`);
  }

  return response.json();
};

// Update user email
export const updateUserEmail = async (email: string, password: string): Promise<UserProfile> => {
  const url = getApiUrl(API_ENDPOINTS.USER_EMAIL);
  
  const response = await fetch(url, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({ email, password })
  });

  if (!response.ok) {
    throw new Error(`Failed to update email: ${response.statusText}`);
  }

  return response.json();
};

// Update user password
export const updateUserPassword = async (currentPassword: string, newPassword: string): Promise<{ success: boolean }> => {
  const url = getApiUrl(API_ENDPOINTS.USER_PASSWORD);
  
  const response = await fetch(url, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({ currentPassword, newPassword })
  });

  if (!response.ok) {
    throw new Error(`Failed to update password: ${response.statusText}`);
  }

  return response.json();
};

// Delete user account
export const deleteUserAccount = async (password: string): Promise<{ success: boolean }> => {
  const url = getApiUrl(API_ENDPOINTS.USER_ACCOUNT);
  
  const response = await fetch(url, {
    method: 'DELETE',
    headers: getAuthHeaders(),
    body: JSON.stringify({ password })
  });

  if (!response.ok) {
    throw new Error(`Failed to delete account: ${response.statusText}`);
  }

  return response.json();
};

// Get user preferences
export const getUserPreferences = async (): Promise<any[]> => {
  const url = getApiUrl(API_ENDPOINTS.USER_PREFERENCES);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: getAuthHeaders()
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch preferences: ${response.statusText}`);
  }

  return response.json();
};

// Update user preferences
export const updateUserPreferences = async (preferences: any[]): Promise<any[]> => {
  const url = getApiUrl(API_ENDPOINTS.USER_PREFERENCES);
  
  const response = await fetch(url, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify({ preferences })
  });

  if (!response.ok) {
    throw new Error(`Failed to update preferences: ${response.statusText}`);
  }

  return response.json();
};
