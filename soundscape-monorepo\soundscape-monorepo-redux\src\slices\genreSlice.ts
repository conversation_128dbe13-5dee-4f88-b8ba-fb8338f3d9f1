import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchGenresAsync, fetchGenreChannels } from '../thunks/genreThunks';
import { enhanceGenreData, enhanceChannelData } from '../utils/dataEnhancers';

// Define interfaces
export interface Genre {
  id: number;
  name: string;
  description?: string;
  image?: string;
  url?: string;
  path?: string;
  order?: number;
  color?: string; // Optional - will be generated if not provided
  icon?: string;  // Optional - will be generated if not provided
  [key: string]: any;
}

export interface Channel {
  id: number; // Changed from string to number to match database
  name: string;
  description?: string;
  image?: string;
  imageUrl?: string; // Computed from image field
  url?: string;
  path?: string;
  order?: number;
  tracks?: any[];
  [key: string]: any;
}

export interface GenreState {
  items: Genre[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null;
  selectedGenre: number | null;
  channelsById: { [genreId: string]: Channel[] };
  channelsLoading: boolean;
  channelsError: string | null;
}

const initialState: GenreState = {
  items: [],
  loading: false,
  error: null,
  lastFetched: null,
  selectedGenre: null,
  channelsById: {},
  channelsLoading: false,
  channelsError: null
};



const genreSlice = createSlice({
  name: 'genres',
  initialState,
  reducers: {
    setSelectedGenre: (state, action: PayloadAction<number | null>) => {
      state.selectedGenre = action.payload;
    }
  },
  extraReducers: (builder) => {
    // Handle fetchGenresAsync
    builder
      .addCase(fetchGenresAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGenresAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Enhance genre data with default colors and icons
        state.items = action.payload.map(enhanceGenreData);
        state.lastFetched = Date.now();
      })
      .addCase(fetchGenresAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string || 'Failed to fetch genres';
      });

    // Handle fetchGenreChannels
    builder
      .addCase(fetchGenreChannels.pending, (state) => {
        state.channelsLoading = true;
        state.channelsError = null;
      })
      .addCase(fetchGenreChannels.fulfilled, (state, action) => {
        state.channelsLoading = false;
        // Extract genreId and channels from the response
        const { genreId } = action.payload;
        const { channels } = action.payload;

        // Enhance channel data and store in the state
        state.channelsById[genreId] = channels.map(enhanceChannelData);
      })
      .addCase(fetchGenreChannels.rejected, (state, action) => {
        state.channelsLoading = false;
        state.channelsError = action.payload as string || 'Failed to fetch genre channels';
      });
  }
});

export const { setSelectedGenre } = genreSlice.actions;
export default genreSlice.reducer;