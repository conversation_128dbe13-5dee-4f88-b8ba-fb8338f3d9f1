import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import streamingService, { StreamingTrack, ChannelTracksResponse } from '../services/streamingService';

// Define interfaces for the state and data models
export interface Track {
  id: string;
  title: string;
  artists?: { name: string }[];
  albumTitle?: string;
  albumImage?: string;
  duration?: number;
  url?: string;
  [key: string]: any;
}

export interface ChannelInfo {
  id: string;
  name: string;
  description?: string;
  [key: string]: any;
}

export interface PlayerState {
  isPlaying: boolean;
  currentTrack: number;
  playlist: Track[];
  channelId: string | null;
  loading: boolean;
  error: string | null;
  progress: number;
  duration: number;
  channelInfo: ChannelInfo | null;
  streamUrl: string | null;
}

export interface StreamParams {
  channelId: string;
  trackIndex: number;
}

const initialState: PlayerState = {
  isPlaying: false,
  currentTrack: 0,
  playlist: [],
  channelId: null,
  loading: false,
  error: null,
  progress: 0,
  duration: 0,
  channelInfo: null,
  streamUrl: null
};

// Updated to match database schema
// Helper function to get API URL from environment variables
const getStreamingApiUrl = (): string => {
  // Use the environment variable if available, otherwise use the proxy
  return import.meta.env.VITE_STREAMING_API_URL ?
    `${import.meta.env.VITE_STREAMING_API_URL}/api` :
    '/api';
};

export const fetchPlaylist = createAsyncThunk<{tracks: Track[]; channelInfo: ChannelInfo}, string>(
  'player/fetchPlaylist',
  async (channelId) => {
    console.log(`Fetching playlist for channel: ${channelId}`);

    const response = await streamingService.getChannelTracks(channelId);

    // Transform streaming tracks to player tracks
    const tracks: Track[] = response.tracks.map((track: StreamingTrack) => ({
      id: track.id.toString(),
      title: track.title,
      artists: track.artists.map(artist => ({ name: artist.name })),
      albumTitle: track.albumTitle,
      albumImage: track.albumImage,
      duration: track.duration,
      url: track.url
    }));

    const channelInfo: ChannelInfo = {
      id: response.channelInfo.id.toString(),
      name: response.channelInfo.name,
      description: response.channelInfo.description
    };

    return { tracks, channelInfo };
  }
);

// New action to start channel streaming
export const startChannelStreaming = createAsyncThunk<{channelId: string; streamUrl: string}, string>(
  'player/startChannelStreaming',
  async (channelId) => {
    console.log(`Starting channel streaming for: ${channelId}`);

    // Fetch the playlist first
    const response = await streamingService.getChannelTracks(channelId);

    // Get the stream URL for the first track
    const streamUrl = streamingService.getStreamUrl(channelId, 0);

    return { channelId, streamUrl };
  }
);

export const skipToNextTrack = createAsyncThunk<number, { channelId: string; currentTrack: number }>(
  'player/skipToNextTrack',
  async ({ channelId, currentTrack }) => {
    console.log(`Skipping to next track for channel: ${channelId}, current: ${currentTrack}`);

    const response = await streamingService.skipTrack(channelId, currentTrack);
    return response.nextTrackIndex;
  }
);

// Define interface for track info response
interface TrackInfoResponse {
  trackIndex: number;
  totalTracks: number;
  track: {
    id: string;
    title: string;
    type: string;
    duration: number;
    [key: string]: any;
  };
}

export const getTrackInfo = createAsyncThunk<TrackInfoResponse, { channelId: string; trackIndex: number }>(
  'player/getTrackInfo',
  async ({ channelId, trackIndex }) => {
    console.log(`Getting track info for channel: ${channelId}, track: ${trackIndex}`);

    const response = await streamingService.getTrackInfo(channelId, trackIndex);
    return response;
  }
);

const playerSlice = createSlice({
  name: 'player',
  initialState,
  reducers: {
    playTrack: (state) => {
      state.isPlaying = true;
    },
    pauseTrack: (state) => {
      state.isPlaying = false;
    },
    nextTrack: (state) => {
      if (state.currentTrack < state.playlist.length - 1) {
        state.currentTrack += 1;
      }
    },
    previousTrack: (state) => {
      if (state.currentTrack > 0) {
        state.currentTrack -= 1;
      }
    },
    setProgress: (state, action: PayloadAction<number>) => {
      state.progress = action.payload;
    },
    setDuration: (state, action: PayloadAction<number>) => {
      state.duration = action.payload;
    },
    setChannelId: (state, action: PayloadAction<string>) => {
      state.channelId = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPlaylist.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPlaylist.fulfilled, (state, action) => {
        state.loading = false;
        state.playlist = action.payload.tracks;
        state.channelInfo = action.payload.channelInfo;
        state.currentTrack = 0;
        state.error = null;
      })
      .addCase(fetchPlaylist.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch playlist';
      })
      .addCase(startChannelStreaming.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startChannelStreaming.fulfilled, (state, action) => {
        state.loading = false;
        state.channelId = action.payload.channelId;
        state.streamUrl = action.payload.streamUrl;
        state.currentTrack = 0;
        state.isPlaying = true;
        state.error = null;
      })
      .addCase(startChannelStreaming.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to start streaming';
      })
      .addCase(skipToNextTrack.fulfilled, (state, action) => {
        state.currentTrack = action.payload;
        state.error = null;
      })
      .addCase(skipToNextTrack.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to skip track';
      })
      .addCase(getTrackInfo.fulfilled, (state) => {
        // Update any relevant state with track info
        state.error = null;
      })
      .addCase(getTrackInfo.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to get track info';
      });
  }
});

export const {
  playTrack,
  pauseTrack,
  nextTrack,
  previousTrack,
  setProgress,
  setDuration,
  setChannelId
} = playerSlice.actions;

// These are already exported above

export default playerSlice.reducer;
