// soundscape-monorepo-redux/src/store.ts

import { configureStore, Action } from '@reduxjs/toolkit';
import { rootReducer } from './reducers';
import { createLogger } from 'redux-logger';

// Create logger middleware with proper configuration
const loggerMiddleware = createLogger({
  collapsed: true, // Collapse log groups by default
  duration: true, // Print the duration of each action
  timestamp: true, // Print the timestamp of each action
  // Don't log these actions to avoid excessive logs
  predicate: (_: any, action: Action) => !action.type.includes('@@redux')
});

// Create the Redux store with the root reducer
const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) => 
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['FETCH_GENRES_FAILURE'],
        // Ignore these field paths in all actions
        ignoredActionPaths: ['payload.message'],
      },
      immutableCheck: true,
      thunk: true
    }).concat(loggerMiddleware),
  devTools: process.env.NODE_ENV !== 'production'
});

// Explicitly infer the RootState and AppDispatch types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;