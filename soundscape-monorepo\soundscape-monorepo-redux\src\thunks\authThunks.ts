import { Dispatch } from 'redux';
import {
  loginRequest,
  loginSuccess,
  loginFailure,
  signupRequest,
  signupSuccess,
  signupFailure,
  logout as logoutAction,
  AuthAction
} from '../actions/authActions';
import {
  loginWithEmailAndPassword,
  signupWithEmailAndPassword,
  logout as logoutService
} from '../services/authService';

// Login thunk
export const login = (email: string, password: string) => async (dispatch: Dispatch<AuthAction>) => {
  try {
    dispatch(loginRequest());
    const user = await loginWithEmailAndPassword(email, password);
    dispatch(loginSuccess(user));
  } catch (error) {
    dispatch(loginFailure(error instanceof Error ? error.message : 'Login failed'));
  }
};

// Signup thunk
export const signup = (name: string, email: string, password: string) => async (dispatch: Dispatch<AuthAction>) => {
  try {
    dispatch(signupRequest());
    const user = await signupWithEmailAndPassword(name, email, password);
    dispatch(signupSuccess(user));
  } catch (error) {
    dispatch(signupFailure(error instanceof Error ? error.message : 'Signup failed'));
  }
};

// Logout thunk
export const logout = () => async (dispatch: Dispatch<AuthAction>) => {
  try {
    await logoutService();
    dispatch(logoutAction());
  } catch (error) {
    console.error('Logout failed:', error);
    // Still dispatch logout action even if the service call fails
    dispatch(logoutAction());
  }
}; 