import { createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchGenresRequest,
  fetchGenresSuccess,
  fetchGenresFailure
} from '../actions';
import { fetchGenresFromApi, fetchGenreChannelsFromApi } from '../services/genreService';
import { shouldFetchGenres, GenreListState } from '../reducers/genresReducer';
import { enhanceGenreData } from '../utils/dataEnhancers';

// Legacy thunk action creator for fetching genres
export const fetchGenres = () => async (dispatch: any, getState: any) => {
  try {
    // Check if we should fetch new data based on cache state
    const genresState = getState().genres;

    // Check if we need to fetch new data
    if (!shouldFetchGenres(genresState)) {
      // If cache is valid, don't fetch again
      return;
    }

    // Dispatch request action
    dispatch(fetchGenresRequest());

    // Fetch genres from API
    const genres = await fetchGenresFromApi();

    // Validate response data
    if (!genres || !Array.isArray(genres)) {
      throw new Error('Invalid genre data received from API');
    }

    // Enhance genre data and dispatch success action
    const enhancedGenres = genres.map(enhanceGenreData);
    dispatch(fetchGenresSuccess(enhancedGenres));
  } catch (error) {
    // Dispatch failure action with error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    dispatch(fetchGenresFailure(errorMessage));
  }
};

// Modern implementation using createAsyncThunk
export const fetchGenresAsync = createAsyncThunk(
  'genres/fetchGenres',
  async (_, { getState, rejectWithValue }) => {
    try {
      // Check if we should fetch new data based on cache state
      const state = getState() as { genres: GenreListState };

      // Check if we need to fetch new data
      if (!shouldFetchGenres(state.genres)) {
        // If cache is valid, return current items
        return state.genres.items;
      }

      // Fetch genres from API
      const genres = await fetchGenresFromApi();

      // Validate response data
      if (!genres || !Array.isArray(genres)) {
        throw new Error('Invalid genre data received from API');
      }

      // Enhance genre data before returning
      return genres.map(enhanceGenreData);
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error occurred');
    }
  }
);

// Fetch channels for a specific genre
export const fetchGenreChannels = createAsyncThunk(
  'genres/fetchGenreChannels',
  async (genreId: number | string, { rejectWithValue }) => {
    try {
      const data = await fetchGenreChannelsFromApi(genreId);
      return {
        genreId,
        ...data
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Unknown error occurred');
    }
  }
);