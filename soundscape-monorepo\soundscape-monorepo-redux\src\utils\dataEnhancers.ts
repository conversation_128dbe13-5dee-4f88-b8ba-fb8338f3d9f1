import { Genre } from '../actions';

// Helper function to generate default color and icon for genres
export const enhanceGenreData = (genre: any): Genre => {
  const genreColorMap: { [key: string]: { color: string; icon: string } } = {
    'rock': { color: '#e74c3c', icon: '🎸' },
    'jazz': { color: '#9b59b6', icon: '🎷' },
    'pop': { color: '#3498db', icon: '🎤' },
    'classical': { color: '#f39c12', icon: '🎼' },
    'electronic': { color: '#1abc9c', icon: '🎧' },
    'hip-hop': { color: '#34495e', icon: '🎤' },
    'country': { color: '#d35400', icon: '🤠' },
    'blues': { color: '#2c3e50', icon: '🎺' }
  };

  const genreName = genre.name?.toLowerCase() || '';
  const defaultStyle = genreColorMap[genreName] || { color: '#95a5a6', icon: '🎵' };

  return {
    ...genre,
    color: genre.color || defaultStyle.color,
    icon: genre.icon || defaultStyle.icon
  };
};

// Helper function to enhance channel data
export const enhanceChannelData = (channel: any): any => {
  return {
    ...channel,
    imageUrl: channel.image || channel.imageUrl // Map image to imageUrl for backward compatibility
  };
};
