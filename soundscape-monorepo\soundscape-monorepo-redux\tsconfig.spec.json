{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../dist/out-tsc", "module": "commonjs", "moduleResolution": "node10", "jsx": "react-jsx", "types": ["jest", "node"], "composite": true, "declaration": true, "declarationMap": true}, "include": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "src/**/*.d.ts"]}