# Authentication Integration Test Plan

## Overview
This document outlines the testing plan for the comprehensive authentication integration across the soundscape project.

## Test Environment Setup

### 1. Database Setup
```bash
cd soundscape-database
npm install
node SeedDatabase.js
```

### 2. API Services Setup
```bash
# User API (Port 4002)
cd soundscape-api/user
npm install
npm start

# Auth API (Port 4000) - Optional for OAuth
cd soundscape-api/auth
npm install
npm start

# Genres API (Port 4001)
cd soundscape-api/genres
npm install
npm start

# Streaming API (Port 4004)
cd soundscape-api/streaming
npm install
npm start
```

### 3. Web Application Setup
```bash
cd soundscape-monorepo
npm install
nx serve soundscape-web
```

## Test Cases

### 1. Database Integration Tests

#### Test User Seed Data
- **Objective**: Verify test users are created in database
- **Expected Users**:
  - <EMAIL> / admin123 (Admin)
  - <EMAIL> / test123 (Regular User)
  - <EMAIL> / demo123 (Regular User)

#### Test Database Connection
- **Objective**: Verify User API can connect to database
- **Test**: Check User API logs for successful database connection

### 2. API Integration Tests

#### User API Authentication Endpoints
- **POST /api/user/login**
  - Test with valid credentials: `<EMAIL> / admin123`
  - Test with invalid credentials
  - Verify response format includes user data and token

- **POST /api/user/register**
  - Test creating new user
  - Test duplicate email handling
  - Verify response format

#### User API Profile Endpoints
- **GET /api/user/profile**
  - Test with valid token
  - Test with invalid token
  - Verify user data returned

- **PUT /api/user/profile**
  - Test updating user profile
  - Verify changes persist

### 3. Web Application Integration Tests

#### Login Flow
1. Navigate to `/login`
2. Enter credentials: `<EMAIL> / test123`
3. Verify successful login and redirect to home page
4. Check localStorage for authentication token

#### Signup Flow
1. Navigate to `/signup`
2. Create new account
3. Verify successful registration and redirect
4. Check user created in database

#### Account Management
1. Login and navigate to `/account`
2. Verify user profile loads from API
3. Update profile information
4. Verify changes saved via API

#### Logout Flow
1. Click logout button
2. Verify redirect to login page
3. Check localStorage cleared

### 4. End-to-End Integration Tests

#### Complete User Journey
1. **Registration**: Create new account via web app
2. **Login**: Login with new credentials
3. **Profile Management**: Update profile via account page
4. **Session Persistence**: Refresh page, verify still logged in
5. **Logout**: Logout and verify session cleared

#### Error Handling
1. **Network Errors**: Test with API services down
2. **Invalid Credentials**: Test login with wrong password
3. **Expired Sessions**: Test with invalid tokens

## Expected Results

### Successful Integration Indicators
- [ ] Database contains test users after seeding
- [ ] User API starts successfully on port 4002
- [ ] Web app can authenticate users via API
- [ ] User profiles load and update correctly
- [ ] Authentication state persists across page refreshes
- [ ] Logout clears authentication properly

### API Response Formats

#### Login Response
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "Test User",
    "is_active": true,
    "is_admin": false,
    "created_date": "2024-01-01T00:00:00.000Z"
  },
  "token": "<EMAIL>"
}
```

#### Profile Response
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "Test User",
  "description": "Test user for development",
  "phone": "******-0101",
  "is_active": true,
  "is_admin": false,
  "created_date": "2024-01-01T00:00:00.000Z"
}
```

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure PostgreSQL is running and credentials are correct
2. **Port Conflicts**: Check that ports 4000, 4001, 4002, 4004, 4200 are available
3. **CORS Issues**: Verify Vite proxy configuration is correct
4. **Missing Dependencies**: Run `npm install` in all service directories

### Debug Steps
1. Check API service logs for errors
2. Verify database contains seed data
3. Test API endpoints directly with curl/Postman
4. Check browser network tab for failed requests
5. Verify localStorage contains authentication tokens
